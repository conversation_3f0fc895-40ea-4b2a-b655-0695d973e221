#!/usr/bin/env python3
"""
Final Optimization để đạt 90%+ accuracy
Sử dụng stacking ensemble và advanced techniques
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import RobustScaler
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

class FinalOptimizer:
    def __init__(self):
        self.data = None
        self.X = None
        self.y = None
        
    def load_data(self):
        """Load advanced dataset"""
        try:
            self.data = pd.read_csv('data/advanced/advanced_job_candidate_pairs.csv')
            print(f"Loaded {len(self.data)} advanced pairs")
            return True
        except FileNotFoundError:
            print("Error: Advanced dataset not found!")
            return False
    
    def create_ultra_features(self):
        """Create ultra-advanced features"""
        # Exclude ID columns
        feature_cols = [col for col in self.data.columns 
                       if col not in ['pair_id', 'job_id', 'candidate_id', 'suitability_label']]
        
        X = self.data[feature_cols].copy().fillna(0)
        y = self.data['suitability_label']
        
        # Create interaction features for all important combinations
        important_features = ['skill_similarity', 'experience_match', 'education_match', 
                            'semantic_similarity', 'tfidf_unigram', 'tfidf_bigram']
        
        for i, feat1 in enumerate(important_features):
            for feat2 in important_features[i+1:]:
                if feat1 in X.columns and feat2 in X.columns:
                    X[f'{feat1}_x_{feat2}'] = X[feat1] * X[feat2]
                    X[f'{feat1}_plus_{feat2}'] = X[feat1] + X[feat2]
                    X[f'{feat1}_minus_{feat2}'] = abs(X[feat1] - X[feat2])
        
        # Polynomial features
        for feat in important_features:
            if feat in X.columns:
                X[f'{feat}_squared'] = X[feat] ** 2
                X[f'{feat}_sqrt'] = np.sqrt(X[feat])
        
        # Ratio features
        if 'candidate_experience' in X.columns and 'required_experience' in X.columns:
            X['exp_ratio'] = X['candidate_experience'] / (X['required_experience'] + 1)
            X['exp_diff'] = X['candidate_experience'] - X['required_experience']
            X['exp_over_qualified'] = (X['candidate_experience'] > X['required_experience'] + 2).astype(int)
        
        # Skill-based features
        if 'resume_skill_count' in X.columns and 'job_skill_count' in X.columns:
            X['skill_ratio'] = X['resume_skill_count'] / (X['job_skill_count'] + 1)
            X['skill_coverage'] = np.minimum(X['resume_skill_count'], X['job_skill_count']) / (X['job_skill_count'] + 1)
        
        # Text-based features
        if 'resume_length' in X.columns and 'job_length' in X.columns:
            X['text_ratio'] = X['resume_length'] / (X['job_length'] + 1)
            X['text_similarity'] = 1 / (1 + abs(X['resume_length'] - X['job_length']))
        
        print(f"Ultra features created: {X.shape[1]} features")
        return X, y
    
    def smart_sampling(self, X, y):
        """Smart sampling to create better distribution"""
        df_temp = pd.concat([X, pd.Series(y, name='target')], axis=1)
        
        # Get class distributions
        class_counts = df_temp['target'].value_counts()
        print(f"Original distribution: {class_counts.to_dict()}")
        
        # Target distribution: more balanced but realistic
        target_class_0 = min(800, len(df_temp[df_temp['target'] == 0]) * 2)
        target_class_1 = min(1200, len(df_temp[df_temp['target'] == 1]))
        target_class_2 = min(400, len(df_temp[df_temp['target'] == 2]) * 20)  # Boost minority class significantly
        
        balanced_dfs = []
        
        # Class 0: Not Suitable
        class_0_df = df_temp[df_temp['target'] == 0]
        if len(class_0_df) < target_class_0:
            sampled_0 = class_0_df.sample(n=target_class_0, replace=True, random_state=42)
        else:
            sampled_0 = class_0_df.sample(n=target_class_0, random_state=42)
        balanced_dfs.append(sampled_0)
        
        # Class 1: Moderately Suitable
        class_1_df = df_temp[df_temp['target'] == 1]
        sampled_1 = class_1_df.sample(n=target_class_1, random_state=42)
        balanced_dfs.append(sampled_1)
        
        # Class 2: Highly Suitable (aggressive oversampling)
        class_2_df = df_temp[df_temp['target'] == 2]
        if len(class_2_df) < target_class_2:
            sampled_2 = class_2_df.sample(n=target_class_2, replace=True, random_state=42)
        else:
            sampled_2 = class_2_df.sample(n=target_class_2, random_state=42)
        balanced_dfs.append(sampled_2)
        
        # Combine
        balanced_df = pd.concat(balanced_dfs, ignore_index=True)
        
        X_balanced = balanced_df.drop('target', axis=1)
        y_balanced = balanced_df['target']
        
        print(f"Balanced distribution: {y_balanced.value_counts().to_dict()}")
        return X_balanced, y_balanced
    
    def create_stacking_ensemble(self):
        """Create stacking ensemble with best models"""
        
        # Base models (level 0)
        base_models = [
            ('rf', RandomForestClassifier(
                n_estimators=300, max_depth=20, min_samples_split=5,
                min_samples_leaf=2, max_features='sqrt', random_state=42
            )),
            ('xgb', xgb.XGBClassifier(
                n_estimators=300, max_depth=8, learning_rate=0.05,
                subsample=0.9, colsample_bytree=0.8, random_state=42
            )),
            ('gb', GradientBoostingClassifier(
                n_estimators=300, max_depth=8, learning_rate=0.05,
                subsample=0.9, random_state=42
            ))
        ]
        
        # Meta-learner (level 1)
        meta_learner = LogisticRegression(random_state=42, max_iter=1000)
        
        # Stacking classifier
        stacking_model = StackingClassifier(
            estimators=base_models,
            final_estimator=meta_learner,
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )
        
        return stacking_model
    
    def train_final_models(self, X_train, X_test, y_train, y_test):
        """Train final optimized models"""
        
        models = {}
        results = []
        
        # 1. Optimized Random Forest
        print("\nTraining Ultra Random Forest...")
        rf_model = RandomForestClassifier(
            n_estimators=500, max_depth=25, min_samples_split=3,
            min_samples_leaf=1, max_features='sqrt', 
            class_weight='balanced_subsample', random_state=42
        )
        rf_model.fit(X_train, y_train)
        rf_pred = rf_model.predict(X_test)
        rf_acc = accuracy_score(y_test, rf_pred)
        
        models['Ultra Random Forest'] = rf_model
        results.append(('Ultra Random Forest', rf_acc))
        print(f"  Accuracy: {rf_acc:.4f}")
        
        # 2. Optimized XGBoost
        print("\nTraining Ultra XGBoost...")
        xgb_model = xgb.XGBClassifier(
            n_estimators=500, max_depth=10, learning_rate=0.03,
            subsample=0.9, colsample_bytree=0.8, reg_alpha=0.1,
            reg_lambda=1.5, random_state=42
        )
        xgb_model.fit(X_train, y_train)
        xgb_pred = xgb_model.predict(X_test)
        xgb_acc = accuracy_score(y_test, xgb_pred)
        
        models['Ultra XGBoost'] = xgb_model
        results.append(('Ultra XGBoost', xgb_acc))
        print(f"  Accuracy: {xgb_acc:.4f}")
        
        # 3. Stacking Ensemble
        print("\nTraining Stacking Ensemble...")
        stacking_model = self.create_stacking_ensemble()
        stacking_model.fit(X_train, y_train)
        stacking_pred = stacking_model.predict(X_test)
        stacking_acc = accuracy_score(y_test, stacking_pred)
        
        models['Stacking Ensemble'] = stacking_model
        results.append(('Stacking Ensemble', stacking_acc))
        print(f"  Accuracy: {stacking_acc:.4f}")
        
        # 4. Weighted Ensemble (manual)
        print("\nCreating Weighted Ensemble...")
        
        # Get predictions from all models
        rf_proba = rf_model.predict_proba(X_test)
        xgb_proba = xgb_model.predict_proba(X_test)
        stacking_proba = stacking_model.predict_proba(X_test)
        
        # Weighted average (weights based on individual performance)
        weights = [0.3, 0.4, 0.3]  # RF, XGB, Stacking
        weighted_proba = (weights[0] * rf_proba + 
                         weights[1] * xgb_proba + 
                         weights[2] * stacking_proba)
        
        weighted_pred = np.argmax(weighted_proba, axis=1)
        weighted_acc = accuracy_score(y_test, weighted_pred)
        
        results.append(('Weighted Ensemble', weighted_acc))
        print(f"  Accuracy: {weighted_acc:.4f}")
        
        return models, results
    
    def run_final_optimization(self):
        """Run final optimization"""
        print("FINAL OPTIMIZATION FOR 90%+ ACCURACY")
        print("="*60)
        
        if not self.load_data():
            return False
        
        # Create ultra features
        print("\n1. Creating ultra-advanced features...")
        X, y = self.create_ultra_features()
        
        # Smart sampling
        print("\n2. Smart sampling for better distribution...")
        X_balanced, y_balanced = self.smart_sampling(X, y)
        
        # Split data
        print("\n3. Splitting data...")
        X_train, X_test, y_train, y_test = train_test_split(
            X_balanced, y_balanced, test_size=0.2, random_state=42, stratify=y_balanced
        )
        
        print(f"Train: {len(X_train)}, Test: {len(X_test)}")
        print(f"Features: {X_train.shape[1]}")
        
        # Train final models
        print("\n4. Training final optimized models...")
        models, results = self.train_final_models(X_train, X_test, y_train, y_test)
        
        # Results
        print(f"\n" + "="*60)
        print("FINAL OPTIMIZATION RESULTS")
        print("="*60)
        
        results.sort(key=lambda x: x[1], reverse=True)
        
        for model_name, accuracy in results:
            print(f"{model_name:<25}: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        best_model, best_acc = results[0]
        print(f"\nBest Model: {best_model}")
        print(f"Best Accuracy: {best_acc:.4f} ({best_acc*100:.2f}%)")
        
        if best_acc >= 0.90:
            print(f"\n🎉 TARGET ACHIEVED! Accuracy >= 90%")
        else:
            gap = 0.90 - best_acc
            print(f"\n📈 Close to target! Gap: {gap*100:.1f}%")
        
        # Detailed report for best model
        if best_model in models:
            print(f"\nDetailed Report for {best_model}:")
            best_model_obj = models[best_model]
            test_pred = best_model_obj.predict(X_test)
            print(classification_report(y_test, test_pred, 
                                      target_names=['Not Suitable', 'Moderately Suitable', 'Highly Suitable']))
        
        return True

def main():
    """Main function"""
    optimizer = FinalOptimizer()
    optimizer.run_final_optimization()

if __name__ == "__main__":
    main()
