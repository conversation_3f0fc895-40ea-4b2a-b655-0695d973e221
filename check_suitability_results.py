#!/usr/bin/env python3
"""
Kiểm tra kết quả phân loại suitability
Xem chi tiết Not Suitable, Moderately Suitable, và Highly Suitable
"""

import pandas as pd
import numpy as np

class SuitabilityChecker:
    def __init__(self):
        self.pairs_df = None
        self.jobs_df = None
        self.candidates_df = None
        
    def load_data(self):
        """Load dữ liệu cần thiết"""
        try:
            # Load job-candidate pairs
            self.pairs_df = pd.read_csv('data/improved/job_candidate_pairs.csv')
            print(f"Loaded {len(self.pairs_df)} job-candidate pairs")
            
            # Load job descriptions
            self.jobs_df = pd.read_csv('data/itviec_jobs_undetected.csv')
            print(f"Loaded {len(self.jobs_df)} job descriptions")
            
            # Load candidates info
            try:
                self.candidates_df = pd.read_csv('data/improved/candidates.csv')
                print(f"Loaded {len(self.candidates_df)} candidates")
            except FileNotFoundError:
                print("Candidates file not found, will use basic info")
                self.candidates_df = None
            
            return True
            
        except FileNotFoundError as e:
            print(f"Error loading data: {e}")
            print("Please run improved_data_pipeline.py first")
            return False
    
    def show_distribution(self):
        """Hiển thị phân bố suitability"""
        print("\n" + "="*60)
        print("SUITABILITY DISTRIBUTION")
        print("="*60)
        
        label_counts = self.pairs_df['suitability_label'].value_counts().sort_index()
        total = len(self.pairs_df)
        
        labels = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}
        
        for label in [0, 1, 2]:
            count = label_counts.get(label, 0)
            percentage = (count / total) * 100
            print(f"{label} - {labels[label]:<20}: {count:3d} pairs ({percentage:5.1f}%)")
    
    def get_job_info(self, job_id):
        """Lấy thông tin job"""
        job_num = int(job_id.replace('JOB', ''))
        if job_num <= len(self.jobs_df):
            job_row = self.jobs_df.iloc[job_num - 1]
            return {
                'title': job_row.get('title', 'N/A'),
                'company': job_row.get('company', 'N/A'),
                'location': job_row.get('location', 'N/A'),
                'description': str(job_row.get('description', ''))[:200] + "...",
                'skills': job_row.get('skills', 'N/A')
            }
        return {'title': 'N/A', 'company': 'N/A', 'location': 'N/A', 'description': 'N/A', 'skills': 'N/A'}
    
    def get_candidate_info(self, candidate_id):
        """Lấy thông tin candidate"""
        if self.candidates_df is not None:
            candidate_row = self.candidates_df[self.candidates_df['candidate_id'] == candidate_id]
            if not candidate_row.empty:
                row = candidate_row.iloc[0]
                return {
                    'name': row.get('name', candidate_id),
                    'experience_years': row.get('experience_years', 'N/A'),
                    'current_position': row.get('current_position', 'N/A'),
                    'summary': str(row.get('summary', ''))[:200] + "..."
                }
        
        return {
            'name': candidate_id,
            'experience_years': 'N/A',
            'current_position': 'N/A',
            'summary': 'N/A'
        }
    
    def show_category_details(self, suitability_label, limit=5):
        """Hiển thị chi tiết một category"""
        labels = {0: 'NOT SUITABLE', 1: 'MODERATELY SUITABLE', 2: 'HIGHLY SUITABLE'}
        
        # Filter data
        category_data = self.pairs_df[self.pairs_df['suitability_label'] == suitability_label]
        category_data = category_data.sort_values('overall_suitability', ascending=False)
        
        print(f"\n" + "="*80)
        print(f"{labels[suitability_label]} PAIRS")
        print("="*80)
        print(f"Total: {len(category_data)} pairs")
        
        if len(category_data) == 0:
            print("No pairs found in this category")
            return
        
        print(f"\nShowing top {min(limit, len(category_data))} pairs:")
        print("-"*80)
        
        for idx, (_, row) in enumerate(category_data.head(limit).iterrows()):
            print(f"\n{idx+1}. PAIR ID: {row['pair_id']}")
            print(f"   Overall Suitability: {row['overall_suitability']:.3f}")
            
            # Job info
            job_info = self.get_job_info(row['job_id'])
            print(f"   JOB {row['job_id']}:")
            print(f"     Title: {job_info['title']}")
            print(f"     Company: {job_info['company']}")
            print(f"     Location: {job_info['location']}")
            
            # Candidate info
            candidate_info = self.get_candidate_info(row['candidate_id'])
            print(f"   CANDIDATE {row['candidate_id']}:")
            print(f"     Name: {candidate_info['name']}")
            print(f"     Experience: {candidate_info['experience_years']} years")
            print(f"     Position: {candidate_info['current_position']}")
            
            # Similarity scores
            print(f"   SIMILARITY SCORES:")
            print(f"     Primary Skills: {row['primary_skills_jaccard']:.3f}")
            print(f"     Secondary Skills: {row['secondary_skills_jaccard']:.3f}")
            print(f"     Adjectives: {row['adjectives_jaccard']:.3f}")
            print(f"     Adverbs: {row['adverbs_jaccard']:.3f}")
            
            # Skill counts
            print(f"   SKILL MATCHING:")
            print(f"     Job Primary Skills: {row['job_primary_count']}")
            print(f"     Candidate Primary Skills: {row['candidate_primary_count']}")
            print(f"     Primary Skills Match: {row['primary_intersection']}")
            print(f"     Job Secondary Skills: {row['job_secondary_count']}")
            print(f"     Candidate Secondary Skills: {row['candidate_secondary_count']}")
            print(f"     Secondary Skills Match: {row['secondary_intersection']}")
            
            print("-"*80)
    
    def show_score_ranges(self):
        """Hiển thị phạm vi điểm số cho mỗi category"""
        print(f"\n" + "="*60)
        print("SCORE RANGES BY CATEGORY")
        print("="*60)
        
        labels = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}
        
        for label in [0, 1, 2]:
            category_data = self.pairs_df[self.pairs_df['suitability_label'] == label]
            if len(category_data) > 0:
                min_score = category_data['overall_suitability'].min()
                max_score = category_data['overall_suitability'].max()
                mean_score = category_data['overall_suitability'].mean()
                print(f"{labels[label]:<20}: {min_score:.3f} - {max_score:.3f} (avg: {mean_score:.3f})")
            else:
                print(f"{labels[label]:<20}: No data")
    
    def search_by_job_or_candidate(self, search_id):
        """Tìm kiếm theo job ID hoặc candidate ID"""
        print(f"\n" + "="*60)
        print(f"SEARCH RESULTS FOR: {search_id}")
        print("="*60)
        
        if search_id.startswith('JOB'):
            results = self.pairs_df[self.pairs_df['job_id'] == search_id]
            print(f"Found {len(results)} candidates for {search_id}")
        elif search_id.startswith('CAND'):
            results = self.pairs_df[self.pairs_df['candidate_id'] == search_id]
            print(f"Found {len(results)} jobs for {search_id}")
        else:
            print("Invalid ID format. Use JOB001 or CAND001 format")
            return
        
        if len(results) == 0:
            print("No results found")
            return
        
        # Sort by suitability score
        results = results.sort_values('overall_suitability', ascending=False)
        
        labels = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}
        
        for _, row in results.iterrows():
            suitability_name = labels[row['suitability_label']]
            print(f"  {row['job_id']} + {row['candidate_id']}: {row['overall_suitability']:.3f} ({suitability_name})")
    
    def interactive_menu(self):
        """Menu tương tác"""
        while True:
            print(f"\n" + "="*60)
            print("SUITABILITY CHECKER - INTERACTIVE MENU")
            print("="*60)
            print("1. Show distribution overview")
            print("2. Show Not Suitable pairs")
            print("3. Show Moderately Suitable pairs")
            print("4. Show Highly Suitable pairs")
            print("5. Show score ranges")
            print("6. Search by Job ID (e.g., JOB001)")
            print("7. Search by Candidate ID (e.g., CAND001)")
            print("8. Exit")
            
            choice = input("\nEnter your choice (1-8): ").strip()
            
            if choice == '1':
                self.show_distribution()
                self.show_score_ranges()
            elif choice == '2':
                limit = int(input("How many pairs to show? (default 5): ") or "5")
                self.show_category_details(0, limit)
            elif choice == '3':
                limit = int(input("How many pairs to show? (default 5): ") or "5")
                self.show_category_details(1, limit)
            elif choice == '4':
                limit = int(input("How many pairs to show? (default 5): ") or "5")
                self.show_category_details(2, limit)
            elif choice == '5':
                self.show_score_ranges()
            elif choice == '6':
                job_id = input("Enter Job ID (e.g., JOB001): ").strip().upper()
                self.search_by_job_or_candidate(job_id)
            elif choice == '7':
                candidate_id = input("Enter Candidate ID (e.g., CAND001): ").strip().upper()
                self.search_by_job_or_candidate(candidate_id)
            elif choice == '8':
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please try again.")

def main():
    """Main function"""
    print("SUITABILITY RESULTS CHECKER")
    print("="*60)
    
    checker = SuitabilityChecker()
    
    # Load data
    if not checker.load_data():
        return
    
    # Show quick overview
    checker.show_distribution()
    checker.show_score_ranges()
    
    # Start interactive menu
    checker.interactive_menu()

if __name__ == "__main__":
    main()
