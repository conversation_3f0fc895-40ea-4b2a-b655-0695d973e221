@echo off
REM AI Job Matching Training Workflow

echo Starting AI Job Matching Training Pipeline
echo ==============================================

echo.
echo Step 1: Extract skills from real data...
python simple_skill_extractor.py

echo.
echo Step 2: Run improved data pipeline...
python improved_data_pipeline.py

echo.
echo Step 3: Train ML models...
python train_models.py

echo.
echo Training completed!
echo Check data/improved/ for results
pause
