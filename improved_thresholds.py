
# IMPROVED SUITABILITY THRESHOLDS
# Based on data analysis for better class balance

def create_improved_suitability_labels(overall_suitability):
    """
    Create suitability labels with improved thresholds
    Based on 33/67 percentiles for balanced distribution
    """
    if overall_suitability >= 0.101:
        return 2  # Highly Suitable
    elif overall_suitability >= 0.033:
        return 1  # Moderately Suitable
    else:
        return 0  # Not Suitable

# Usage in improved_data_pipeline.py:
# Replace the current threshold logic with:
suitability_label = create_improved_suitability_labels(overall_suitability)
