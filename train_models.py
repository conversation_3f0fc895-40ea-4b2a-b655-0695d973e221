#!/usr/bin/env python3
"""
Train Machine Learning Models for Job Matching
Dựa trên b<PERSON>i báo: "AI based suitability measurement and prediction between job description and job seeker profiles"

Hu<PERSON><PERSON> luyện 4 models:
1. Linear Regression
2. Decision Tree
3. AdaBoost
4. XGBoost (mục tiêu: 95.14% accuracy)
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import AdaBoostClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import xgboost as xgb
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class JobMatchingModelTrainer:
    def __init__(self):
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.scaler = StandardScaler()
        self.models = {}
        self.results = {}

    def load_data(self, file_path='data/job_candidate_pairs.csv'):
        """Load the job-candidate pairs data"""
        try:
            self.data = pd.read_csv(file_path)
            print(f"Loaded {len(self.data)} job-candidate pairs")
            print(f"Data shape: {self.data.shape}")
            
            # Check label distribution
            label_dist = self.data['suitability_label'].value_counts().sort_index()
            print(f"\nLabel Distribution:")
            for label, count in label_dist.items():
                print(f"  Class {label}: {count} samples ({count/len(self.data)*100:.1f}%)")
            
            return True
        except FileNotFoundError:
            print("Error: job_candidate_pairs.csv not found!")
            print("Please run simple_data_pipeline.py first")
            return False

    def prepare_features(self):
        """Prepare features for machine learning"""
        # Define feature columns
        feature_columns = [
            'primary_skills_jaccard',
            'secondary_skills_jaccard', 
            'adjectives_jaccard',
            'adverbs_jaccard'
        ]
        
        # Extract features and target
        X = self.data[feature_columns].copy()
        y = self.data['suitability_label'].copy()
        
        # Add additional engineered features
        X['total_similarity'] = X.sum(axis=1)
        X['weighted_similarity'] = (0.4 * X['primary_skills_jaccard'] + 
                                   0.3 * X['secondary_skills_jaccard'] + 
                                   0.15 * X['adjectives_jaccard'] + 
                                   0.15 * X['adverbs_jaccard'])
        
        # Add interaction features
        X['primary_secondary_interaction'] = X['primary_skills_jaccard'] * X['secondary_skills_jaccard']
        X['adj_adv_interaction'] = X['adjectives_jaccard'] * X['adverbs_jaccard']
        
        print(f"Feature matrix shape: {X.shape}")
        print(f"Features: {list(X.columns)}")
        
        return X, y

    def split_data(self, test_size=0.2, random_state=42):
        """Split data into train and test sets"""
        X, y = self.prepare_features()
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        # Scale features
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
        print(f"Training set: {len(self.X_train)} samples")
        print(f"Test set: {len(self.X_test)} samples")

    def train_linear_regression(self):
        """Train Linear Regression model"""
        print("\nTraining Linear Regression...")
        
        # For classification, we'll use it as a regressor and round predictions
        model = LinearRegression()
        model.fit(self.X_train_scaled, self.y_train)
        
        # Predictions
        y_pred_train = np.round(np.clip(model.predict(self.X_train_scaled), 0, 2)).astype(int)
        y_pred_test = np.round(np.clip(model.predict(self.X_test_scaled), 0, 2)).astype(int)
        
        # Calculate accuracy
        train_accuracy = accuracy_score(self.y_train, y_pred_train)
        test_accuracy = accuracy_score(self.y_test, y_pred_test)
        
        self.models['Linear Regression'] = model
        self.results['Linear Regression'] = {
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'predictions': y_pred_test
        }
        
        print(f"  Train Accuracy: {train_accuracy:.4f}")
        print(f"  Test Accuracy: {test_accuracy:.4f}")

    def train_decision_tree(self):
        """Train Decision Tree model"""
        print("\nTraining Decision Tree...")
        
        # Grid search for best parameters
        param_grid = {
            'max_depth': [3, 5, 7, 10, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        model = DecisionTreeClassifier(random_state=42)
        grid_search = GridSearchCV(model, param_grid, cv=5, scoring='accuracy', n_jobs=-1)
        grid_search.fit(self.X_train, self.y_train)
        
        best_model = grid_search.best_estimator_
        
        # Predictions
        y_pred_train = best_model.predict(self.X_train)
        y_pred_test = best_model.predict(self.X_test)
        
        # Calculate accuracy
        train_accuracy = accuracy_score(self.y_train, y_pred_train)
        test_accuracy = accuracy_score(self.y_test, y_pred_test)
        
        self.models['Decision Tree'] = best_model
        self.results['Decision Tree'] = {
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'predictions': y_pred_test,
            'best_params': grid_search.best_params_
        }
        
        print(f"  Best params: {grid_search.best_params_}")
        print(f"  Train Accuracy: {train_accuracy:.4f}")
        print(f"  Test Accuracy: {test_accuracy:.4f}")

    def train_adaboost(self):
        """Train AdaBoost model"""
        print("\nTraining AdaBoost...")
        
        # Grid search for best parameters
        param_grid = {
            'n_estimators': [50, 100, 200],
            'learning_rate': [0.01, 0.1, 1.0],
            'algorithm': ['SAMME', 'SAMME.R']
        }
        
        model = AdaBoostClassifier(random_state=42)
        grid_search = GridSearchCV(model, param_grid, cv=5, scoring='accuracy', n_jobs=-1)
        grid_search.fit(self.X_train, self.y_train)
        
        best_model = grid_search.best_estimator_
        
        # Predictions
        y_pred_train = best_model.predict(self.X_train)
        y_pred_test = best_model.predict(self.X_test)
        
        # Calculate accuracy
        train_accuracy = accuracy_score(self.y_train, y_pred_train)
        test_accuracy = accuracy_score(self.y_test, y_pred_test)
        
        self.models['AdaBoost'] = best_model
        self.results['AdaBoost'] = {
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'predictions': y_pred_test,
            'best_params': grid_search.best_params_
        }
        
        print(f"  Best params: {grid_search.best_params_}")
        print(f"  Train Accuracy: {train_accuracy:.4f}")
        print(f"  Test Accuracy: {test_accuracy:.4f}")

    def train_xgboost(self):
        """Train XGBoost model (target: 95.14% accuracy)"""
        print("\nTraining XGBoost...")
        
        # Grid search for best parameters
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [3, 4, 5, 6],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0]
        }
        
        model = xgb.XGBClassifier(random_state=42, eval_metric='mlogloss')
        grid_search = GridSearchCV(model, param_grid, cv=5, scoring='accuracy', n_jobs=-1)
        grid_search.fit(self.X_train, self.y_train)
        
        best_model = grid_search.best_estimator_
        
        # Predictions
        y_pred_train = best_model.predict(self.X_train)
        y_pred_test = best_model.predict(self.X_test)
        
        # Calculate accuracy
        train_accuracy = accuracy_score(self.y_train, y_pred_train)
        test_accuracy = accuracy_score(self.y_test, y_pred_test)
        
        self.models['XGBoost'] = best_model
        self.results['XGBoost'] = {
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'predictions': y_pred_test,
            'best_params': grid_search.best_params_
        }
        
        print(f"  Best params: {grid_search.best_params_}")
        print(f"  Train Accuracy: {train_accuracy:.4f}")
        print(f"  Test Accuracy: {test_accuracy:.4f}")
        
        # Feature importance
        feature_importance = best_model.feature_importances_
        feature_names = self.X_train.columns
        
        print(f"\n  Feature Importance:")
        for name, importance in zip(feature_names, feature_importance):
            print(f"    {name}: {importance:.4f}")

    def train_all_models(self):
        """Train all models"""
        print("TRAINING ALL MODELS")
        print("=" * 60)
        
        self.train_linear_regression()
        self.train_decision_tree()
        self.train_adaboost()
        self.train_xgboost()

    def compare_models(self):
        """Compare all models"""
        print("\n" + "=" * 60)
        print("MODEL COMPARISON")
        print("=" * 60)
        
        comparison_data = []
        for model_name, results in self.results.items():
            comparison_data.append({
                'Model': model_name,
                'Train Accuracy': f"{results['train_accuracy']:.4f}",
                'Test Accuracy': f"{results['test_accuracy']:.4f}",
                'Difference': f"{results['train_accuracy'] - results['test_accuracy']:.4f}"
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        print(comparison_df.to_string(index=False))
        
        # Find best model
        best_model_name = max(self.results.keys(), 
                             key=lambda x: self.results[x]['test_accuracy'])
        best_accuracy = self.results[best_model_name]['test_accuracy']
        
        print(f"\nBest Model: {best_model_name}")
        print(f"Best Test Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
        
        # Check if we reached the target accuracy
        target_accuracy = 0.9514  # 95.14%
        if best_accuracy >= target_accuracy:
            print(f"TARGET ACHIEVED! Accuracy >= {target_accuracy*100:.2f}%")
        else:
            print(f"Target not reached. Need {target_accuracy*100:.2f}%, got {best_accuracy*100:.2f}%")
            print("Suggestions:")
            print("   - Add more features (TF-IDF, experience matching, etc.)")
            print("   - Collect more training data")
            print("   - Try ensemble methods")

    def generate_detailed_report(self):
        """Generate detailed classification report for best model"""
        best_model_name = max(self.results.keys(), 
                             key=lambda x: self.results[x]['test_accuracy'])
        
        print(f"\nDETAILED REPORT FOR {best_model_name}")
        print("=" * 60)
        
        y_pred = self.results[best_model_name]['predictions']
        
        # Classification report
        print("Classification Report:")
        unique_labels = sorted(list(set(self.y_test) | set(y_pred)))
        target_names = ['Not Suitable', 'Moderately Suitable', 'Highly Suitable']
        target_names_filtered = [target_names[i] for i in unique_labels]
        print(classification_report(self.y_test, y_pred,
                                  labels=unique_labels,
                                  target_names=target_names_filtered))
        
        # Confusion matrix
        print("\nConfusion Matrix:")
        cm = confusion_matrix(self.y_test, y_pred)
        print(cm)

    def save_results(self):
        """Save model results to CSV"""
        results_data = []
        for model_name, results in self.results.items():
            results_data.append({
                'Model': model_name,
                'Train_Accuracy': results['train_accuracy'],
                'Test_Accuracy': results['test_accuracy'],
                'Overfitting': results['train_accuracy'] - results['test_accuracy']
            })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_csv('data/model_results.csv', index=False)
        print(f"\nResults saved to data/model_results.csv")

    def run_complete_training(self):
        """Run complete model training pipeline"""
        print("STARTING MODEL TRAINING PIPELINE")
        print("=" * 60)
        
        # Load data
        if not self.load_data():
            return False
        
        # Split data
        print("\n1. Preparing data...")
        self.split_data()
        
        # Train models
        print("\n2. Training models...")
        self.train_all_models()
        
        # Compare models
        print("\n3. Comparing models...")
        self.compare_models()
        
        # Generate detailed report
        self.generate_detailed_report()
        
        # Save results
        self.save_results()
        
        print("\nMODEL TRAINING COMPLETED!")
        return True

if __name__ == "__main__":
    trainer = JobMatchingModelTrainer()
    trainer.run_complete_training()
