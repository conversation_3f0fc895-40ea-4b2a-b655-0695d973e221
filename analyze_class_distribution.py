#!/usr/bin/env python3
"""
Phân tích Class Distribution cho Suitability Labels
So s<PERSON>h với bài báo nghiên cứu và đề xuất cải tiến
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter

def analyze_current_distribution():
    """Phân tích distribution hiện tại"""
    print("📊 ANALYZING CURRENT CLASS DISTRIBUTION")
    print("=" * 60)
    
    # Load data
    try:
        df = pd.read_csv('data/job_candidate_pairs.csv')
        print(f"✓ Loaded {len(df)} job-candidate pairs")
    except FileNotFoundError:
        print("❌ File not found. Please run improved_data_pipeline.py first")
        return None
    
    # Analyze suitability labels
    label_counts = df['suitability_label'].value_counts().sort_index()
    total_pairs = len(df)
    
    print(f"\n📈 CURRENT CLASS DISTRIBUTION:")
    print("-" * 40)
    
    label_names = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}
    
    for label in [0, 1, 2]:
        count = label_counts.get(label, 0)
        percentage = (count / total_pairs) * 100
        print(f"  {label} ({label_names[label]:<20}): {count:3d} pairs ({percentage:5.1f}%)")
    
    # Analyze score distribution
    print(f"\n📊 SCORE DISTRIBUTION ANALYSIS:")
    print("-" * 40)
    
    score_stats = df['overall_suitability'].describe()
    print(f"  Mean score: {score_stats['mean']:.3f}")
    print(f"  Std score:  {score_stats['std']:.3f}")
    print(f"  Min score:  {score_stats['min']:.3f}")
    print(f"  Max score:  {score_stats['max']:.3f}")
    print(f"  Median:     {score_stats['50%']:.3f}")
    
    # Current thresholds
    print(f"\n🎯 CURRENT THRESHOLDS:")
    print("-" * 40)
    print(f"  Not Suitable:        score < 0.15")
    print(f"  Moderately Suitable: 0.15 ≤ score < 0.3")
    print(f"  Highly Suitable:     score ≥ 0.3")
    
    return df, label_counts

def research_paper_comparison():
    """So sánh với bài báo nghiên cứu"""
    print(f"\n📚 RESEARCH PAPER COMPARISON")
    print("=" * 60)
    
    print(f"🔬 Research Paper Findings:")
    print(f"  - Achieved 95.14% accuracy with XGBoost")
    print(f"  - Used 3-class suitability classification")
    print(f"  - Applied Jaccard similarity for 4 clusters")
    print(f"  - BUT: No specific class distribution mentioned")
    
    print(f"\n💡 Typical ML Best Practices:")
    print(f"  - Balanced classes: ~33% each (ideal)")
    print(f"  - Imbalanced acceptable: 20-60% range")
    print(f"  - Severely imbalanced: >80% in one class")

def suggest_improvements(df, label_counts):
    """Đề xuất cải tiến class distribution"""
    print(f"\n💡 IMPROVEMENT SUGGESTIONS")
    print("=" * 60)
    
    total_pairs = len(df)
    
    # Calculate current imbalance
    max_class_pct = max(label_counts) / total_pairs * 100
    min_class_pct = min(label_counts) / total_pairs * 100
    
    print(f"📊 Current Imbalance Analysis:")
    print(f"  Largest class: {max_class_pct:.1f}%")
    print(f"  Smallest class: {min_class_pct:.1f}%")
    print(f"  Imbalance ratio: {max_class_pct/min_class_pct:.1f}:1")
    
    if max_class_pct > 70:
        print(f"\n⚠️  SEVERELY IMBALANCED DATASET!")
        print(f"  Problem: {max_class_pct:.1f}% in dominant class")
        print(f"  Impact: Model will be biased toward majority class")
        
        print(f"\n🔧 RECOMMENDED SOLUTIONS:")
        
        # Solution 1: Adjust thresholds
        print(f"\n1. ADJUST THRESHOLDS:")
        scores = df['overall_suitability'].values
        
        # Calculate better thresholds using percentiles
        threshold_low = np.percentile(scores, 33)
        threshold_high = np.percentile(scores, 67)
        
        print(f"   Current: < 0.15, 0.15-0.3, ≥ 0.3")
        print(f"   Suggested: < {threshold_low:.3f}, {threshold_low:.3f}-{threshold_high:.3f}, ≥ {threshold_high:.3f}")
        
        # Test new distribution
        new_labels = []
        for score in scores:
            if score < threshold_low:
                new_labels.append(0)
            elif score < threshold_high:
                new_labels.append(1)
            else:
                new_labels.append(2)
        
        new_counts = Counter(new_labels)
        print(f"   New distribution:")
        for label in [0, 1, 2]:
            count = new_counts.get(label, 0)
            pct = count / len(scores) * 100
            print(f"     {label}: {count} pairs ({pct:.1f}%)")
        
        # Solution 2: Data augmentation
        print(f"\n2. DATA AUGMENTATION:")
        print(f"   - Generate more job-candidate pairs")
        print(f"   - Focus on underrepresented classes")
        print(f"   - Use SMOTE or similar techniques")
        
        # Solution 3: Weighted training
        print(f"\n3. WEIGHTED TRAINING:")
        print(f"   - Use class_weight='balanced' in sklearn")
        print(f"   - Apply cost-sensitive learning")
        print(f"   - Focus on minority class performance")
        
    elif max_class_pct > 50:
        print(f"\n⚠️  MODERATELY IMBALANCED")
        print(f"  Acceptable but could be improved")
        print(f"  Consider minor threshold adjustments")
        
    else:
        print(f"\n✅ WELL BALANCED DATASET")
        print(f"  Good distribution for training")

def create_improved_thresholds(df):
    """Tạo thresholds cải tiến"""
    print(f"\n🔧 CREATING IMPROVED THRESHOLDS")
    print("=" * 60)
    
    scores = df['overall_suitability'].values
    
    # Method 1: Equal percentiles (33%, 67%)
    threshold_low_33 = np.percentile(scores, 33)
    threshold_high_67 = np.percentile(scores, 67)
    
    # Method 2: Quartiles (25%, 75%)
    threshold_low_25 = np.percentile(scores, 25)
    threshold_high_75 = np.percentile(scores, 75)
    
    # Method 3: Based on score distribution
    mean_score = np.mean(scores)
    std_score = np.std(scores)
    threshold_low_std = mean_score - 0.5 * std_score
    threshold_high_std = mean_score + 0.5 * std_score
    
    methods = [
        ("33/67 Percentiles", threshold_low_33, threshold_high_67),
        ("25/75 Percentiles", threshold_low_25, threshold_high_75),
        ("Mean ± 0.5*Std", threshold_low_std, threshold_high_std),
    ]
    
    print(f"📊 THRESHOLD COMPARISON:")
    print(f"{'Method':<20} {'Low':<8} {'High':<8} {'Distribution'}")
    print("-" * 60)
    
    for method_name, low, high in methods:
        # Calculate distribution
        labels = []
        for score in scores:
            if score < low:
                labels.append(0)
            elif score < high:
                labels.append(1)
            else:
                labels.append(2)
        
        counts = Counter(labels)
        dist_str = f"{counts[0]}-{counts[1]}-{counts[2]}"
        
        print(f"{method_name:<20} {low:<8.3f} {high:<8.3f} {dist_str}")
    
    # Recommend best method
    print(f"\n💡 RECOMMENDATION:")
    print(f"  Use 33/67 percentiles for most balanced distribution")
    print(f"  Thresholds: < {threshold_low_33:.3f}, {threshold_low_33:.3f}-{threshold_high_67:.3f}, ≥ {threshold_high_67:.3f}")
    
    return threshold_low_33, threshold_high_67

def save_improved_pipeline_code(threshold_low, threshold_high):
    """Tạo code cải tiến cho pipeline"""
    improved_code = f'''
# IMPROVED SUITABILITY THRESHOLDS
# Based on data analysis for better class balance

def create_improved_suitability_labels(overall_suitability):
    """
    Create suitability labels with improved thresholds
    Based on 33/67 percentiles for balanced distribution
    """
    if overall_suitability >= {threshold_high:.3f}:
        return 2  # Highly Suitable
    elif overall_suitability >= {threshold_low:.3f}:
        return 1  # Moderately Suitable
    else:
        return 0  # Not Suitable

# Usage in improved_data_pipeline.py:
# Replace the current threshold logic with:
suitability_label = create_improved_suitability_labels(overall_suitability)
'''
    
    with open('improved_thresholds.py', 'w') as f:
        f.write(improved_code)
    
    print(f"\n💾 Saved improved thresholds to 'improved_thresholds.py'")

def main():
    """Main analysis function"""
    print("🔍 CLASS DISTRIBUTION ANALYSIS")
    print("=" * 60)
    
    # Analyze current distribution
    result = analyze_current_distribution()
    if result is None:
        return
    
    df, label_counts = result
    
    # Compare with research paper
    research_paper_comparison()
    
    # Suggest improvements
    suggest_improvements(df, label_counts)
    
    # Create improved thresholds
    threshold_low, threshold_high = create_improved_thresholds(df)
    
    # Save improved code
    save_improved_pipeline_code(threshold_low, threshold_high)
    
    print(f"\n" + "=" * 60)
    print("✅ CLASS DISTRIBUTION ANALYSIS COMPLETED!")
    print("=" * 60)
    
    print(f"\n🎯 KEY FINDINGS:")
    print(f"1. Current distribution may be imbalanced")
    print(f"2. Research paper doesn't specify ideal distribution")
    print(f"3. Improved thresholds suggested for better balance")
    print(f"4. Multiple solutions available for imbalanced data")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Review improved_thresholds.py")
    print(f"2. Update improved_data_pipeline.py with new thresholds")
    print(f"3. Re-run pipeline and compare results")
    print(f"4. Train models with balanced data")

if __name__ == "__main__":
    main()
