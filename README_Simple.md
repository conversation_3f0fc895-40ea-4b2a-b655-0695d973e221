# 🤖 AI Job Matching - Simple Pipeline

Pipeline đơn giản để xây dựng mô hình AI Job Matching dựa trên bài báo nghiên cứu:

**"AI based suitability measurement and prediction between job description and job seeker profiles"**

## 📁 Dữ Liệu Đầu Vào

Bạn cần 2 file CSV trong thư mục `data/`:

1. **`itviec_jobs_undetected.csv`** - Job Descriptions
   ```csv
   id,title,company,location,salary,work_type,description,requirements,skills
   ```

2. **`UpdatedResumeDataSet.csv`** - Candidate Resumes
   ```csv
   Category,Resume
   ```

## 🚀 Cách Sử Dụng

### Bước 1: Cài Đặt Dependencies
```bash
pip install pandas numpy scikit-learn xgboost matplotlib seaborn nltk
```

### Bước 2: Chạy Pipeline Chuẩn Bị Dữ Liệu
```bash
python simple_data_pipeline.py
```

### Bước 3: Huấn <PERSON>n Models
```bash
python train_models.py
```

## 📊 File CSV Được Tạo Ra

### 1. **Job Skills Files**
- `job_primary_skills.csv` - Kỹ năng chính từ job descriptions
- `job_secondary_skills.csv` - Kỹ năng phụ từ job descriptions  
- `job_adjectives.csv` - Tính từ từ job descriptions
- `job_adverbs.csv` - Trạng từ từ job descriptions

### 2. **Candidate Skills Files**
- `candidate_primary_skills.csv` - Kỹ năng chính từ resumes
- `candidate_secondary_skills.csv` - Kỹ năng phụ từ resumes
- `candidate_adjectives.csv` - Tính từ từ resumes
- `candidate_adverbs.csv` - Trạng từ từ resumes
- `candidates.csv` - Thông tin candidates

### 3. **Combined Files**
- `primary_skills.csv` - Tất cả primary skills (job + candidate)
- `secondary_skills.csv` - Tất cả secondary skills (job + candidate)
- `adjectives.csv` - Tất cả adjectives (job + candidate)
- `adverbs.csv` - Tất cả adverbs (job + candidate)

### 4. **Main Training File**
- `job_candidate_pairs.csv` - File chính cho machine learning

**Cấu trúc `job_candidate_pairs.csv`:**
```csv
pair_id,job_id,candidate_id,primary_skills_jaccard,secondary_skills_jaccard,adjectives_jaccard,adverbs_jaccard,overall_suitability,suitability_label
1,JOB001,CAND001,0.8,0.6,0.7,0.9,0.75,2
```

## 🎯 Suitability Labels

- **0**: Not Suitable (overall_suitability < 0.5)
- **1**: Moderately Suitable (0.5 ≤ overall_suitability < 0.75)
- **2**: Highly Suitable (overall_suitability ≥ 0.75)

## 🧮 Công Thức Tính Toán

### Jaccard Similarity
```
J(A,B) = |A ∩ B| / |A ∪ B|
```

### Overall Suitability Score
```
Overall = 0.4 × Primary_Jaccard + 0.3 × Secondary_Jaccard + 0.15 × Adjectives_Jaccard + 0.15 × Adverbs_Jaccard
```

## 🤖 Machine Learning Models

Pipeline huấn luyện 4 models như trong bài báo:

1. **Linear Regression**
2. **Decision Tree** (với Grid Search)
3. **AdaBoost** (với Grid Search)
4. **XGBoost** (mục tiêu: 95.14% accuracy)

### Model Results
Sau khi chạy `train_models.py`, bạn sẽ có:
- `model_results.csv` - Kết quả so sánh các models
- Detailed classification report
- Feature importance (cho XGBoost)

## 📈 Ví Dụ Output

```
🚀 STARTING SIMPLE AI JOB MATCHING PIPELINE
============================================================

1. Loading data...
✓ Loaded 1000 job descriptions
✓ Loaded 2484 candidate resumes

2. Extracting skills from job descriptions...
3. Extracting skills from candidate resumes...
4. Creating job-candidate pairs...
5. Saving data to CSV files...

✅ PIPELINE COMPLETED SUCCESSFULLY!
============================================================

Generated Files:
📁 Job Skills:
   - job_primary_skills.csv: 150 records
   - job_secondary_skills.csv: 200 records
   - job_adjectives.csv: 100 records
   - job_adverbs.csv: 80 records

📁 Candidate Skills:
   - candidate_primary_skills.csv: 300 records
   - candidate_secondary_skills.csv: 400 records
   - candidate_adjectives.csv: 200 records
   - candidate_adverbs.csv: 150 records
   - candidates.csv: 100 records

📁 Combined Data:
   - job_candidate_pairs.csv: 5000 records

📊 Suitability Label Distribution:
   Not Suitable (0): 2000
   Moderately Suitable (1): 2500
   Highly Suitable (2): 500
```

## 🔧 Tùy Chỉnh

### Thay Đổi Số Lượng Candidates
```python
# Trong simple_data_pipeline.py, dòng 142
if idx >= 100:  # Thay đổi số này
    break
```

### Thêm Skills Keywords
```python
# Trong simple_data_pipeline.py
self.primary_skills.extend(['new_skill_1', 'new_skill_2'])
```

### Thay Đổi Weights
```python
# Trong simple_data_pipeline.py, dòng 285
overall_suitability = (0.4 * primary_jaccard + 0.3 * secondary_jaccard + 
                      0.15 * adjectives_jaccard + 0.15 * adverbs_jaccard)
```

## 📊 Kiểm Tra Kết Quả

```python
import pandas as pd

# Load kết quả
pairs_df = pd.read_csv('data/job_candidate_pairs.csv')
results_df = pd.read_csv('data/model_results.csv')

print("Data shape:", pairs_df.shape)
print("Label distribution:")
print(pairs_df['suitability_label'].value_counts())

print("\nModel Results:")
print(results_df)
```

## 🎯 Mục Tiêu

- ✅ Tạo được 4 clusters như bài báo (Primary Skills, Secondary Skills, Adjectives, Adverbs)
- ✅ Tính toán Jaccard Similarity
- ✅ Tạo Overall Suitability Score
- ✅ Huấn luyện 4 models
- 🎯 Đạt 95.14% accuracy với XGBoost

## 🐛 Troubleshooting

### Lỗi "File not found"
```bash
# Đảm bảo có 2 file trong thư mục data/
ls data/
# Phải có: itviec_jobs_undetected.csv và UpdatedResumeDataSet.csv
```

### Lỗi NLTK
```bash
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
```

### Lỗi XGBoost
```bash
pip install xgboost
```

## 📚 Tài Liệu Tham Khảo

- Bài báo gốc: "AI based suitability measurement and prediction between job description and job seeker profiles"
- Scikit-learn: https://scikit-learn.org/
- XGBoost: https://xgboost.readthedocs.io/

---

**Chúc bạn thành công! 🎉**

Nếu có vấn đề gì, hãy kiểm tra:
1. File input có đúng format không
2. Dependencies đã cài đủ chưa
3. Chạy theo đúng thứ tự: `simple_data_pipeline.py` → `train_models.py`
