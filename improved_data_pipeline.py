#!/usr/bin/env python3
"""
Improved Data Pipeline - Sử dụng skills thực tế đã trích xuất
Thay thế hard-coded keywords bằng skills từ dữ liệu thật
"""

import pandas as pd
import numpy as np
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.model_selection import train_test_split
import random
import os
from datetime import datetime

# Import extracted skills
from extracted_skills import PRIMARY_SKILLS, SECONDARY_SKILLS, ADJECTIVES, ADVERBS

class ImprovedJobMatchingPipeline:
    def __init__(self):
        self.jobs_df = None
        self.resumes_df = None
        
        # Use extracted skills from real data
        self.primary_skills = PRIMARY_SKILLS
        self.secondary_skills = SECONDARY_SKILLS
        self.adjectives = ADJECTIVES
        self.adverbs = ADVERBS
        
        print(f"Using extracted skills:")
        print(f"  Primary skills: {len(self.primary_skills)}")
        print(f"  Secondary skills: {len(self.secondary_skills)}")
        print(f"  Adjectives: {len(self.adjectives)}")
        print(f"  Adverbs: {len(self.adverbs)}")

    def load_data(self):
        """Load job and resume data"""
        try:
            self.jobs_df = pd.read_csv('data/itviec_jobs_undetected.csv')
            self.resumes_df = pd.read_csv('data/UpdatedResumeDataSet.csv')
            
            print(f"Loaded {len(self.jobs_df)} job descriptions")
            print(f"Loaded {len(self.resumes_df)} candidate resumes")
            
            return True
        except FileNotFoundError as e:
            print(f"Error loading data: {e}")
            return False

    def clean_text(self, text):
        """Clean and preprocess text"""
        if pd.isna(text):
            return ""
        
        text = str(text).lower()
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        text = ' '.join(text.split())
        return text

    def extract_skills_from_text(self, text, skill_list):
        """Extract skills from text with better matching"""
        if pd.isna(text):
            return []
        
        text_clean = self.clean_text(text)
        found_skills = []
        
        for skill in skill_list:
            skill_clean = skill.lower().strip()
            
            # Exact match
            if skill_clean in text_clean:
                found_skills.append(skill)
            # Partial match for compound skills
            elif ' ' in skill_clean:
                skill_parts = skill_clean.split()
                if all(part in text_clean for part in skill_parts):
                    found_skills.append(skill)
            # Handle variations (e.g., "nodejs" vs "node.js")
            elif skill_clean == 'nodejs' and ('node.js' in text_clean or 'node js' in text_clean):
                found_skills.append(skill)
            elif skill_clean == 'javascript' and ('js' in text_clean.split()):
                found_skills.append(skill)
        
        return found_skills

    def extract_words_from_text(self, text, word_list):
        """Extract adjectives/adverbs from text with frequency"""
        if pd.isna(text):
            return []
        
        text_clean = self.clean_text(text)
        found_words = []
        
        for word in word_list:
            word_clean = word.lower().strip()
            
            # Count occurrences
            if word_clean in text_clean:
                count = text_clean.count(word_clean)
                found_words.extend([word] * count)
        
        return found_words

    def create_job_skills_data(self):
        """Extract skills from job descriptions"""
        primary_skills_data = []
        secondary_skills_data = []
        adjectives_data = []
        adverbs_data = []
        
        for idx, row in self.jobs_df.iterrows():
            job_id = f"JOB{str(row['id']).zfill(3)}"
            
            # Combine all job text
            job_text = f"{row['description']} {row['requirements']} {row['skills']}"
            
            # Extract primary skills
            primary_skills = self.extract_skills_from_text(job_text, self.primary_skills)
            for skill in set(primary_skills):
                frequency = primary_skills.count(skill)
                importance = min(0.9, 0.5 + (frequency * 0.1))  # Higher frequency = higher importance
                
                primary_skills_data.append({
                    'id': len(primary_skills_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'skill_name': skill,
                    'skill_level': 'Required',
                    'years_experience': 'NA',
                    'importance_weight': round(importance, 2),
                    'skill_category': self.categorize_skill(skill),
                    'frequency': frequency
                })
            
            # Extract secondary skills
            secondary_skills = self.extract_skills_from_text(job_text, self.secondary_skills)
            for skill in set(secondary_skills):
                frequency = secondary_skills.count(skill)
                importance = min(0.8, 0.3 + (frequency * 0.1))
                
                secondary_skills_data.append({
                    'id': len(secondary_skills_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'skill_name': skill,
                    'proficiency_level': 'Preferred' if frequency == 1 else 'Required',
                    'importance_weight': round(importance, 2),
                    'skill_category': self.categorize_skill(skill),
                    'frequency': frequency
                })
            
            # Extract adjectives
            adjectives = self.extract_words_from_text(job_text, self.adjectives)
            for adj in set(adjectives):
                frequency = adjectives.count(adj)
                
                adjectives_data.append({
                    'id': len(adjectives_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'adjective': adj,
                    'context': 'job_requirements',
                    'frequency': frequency,
                    'sentiment_score': round(random.uniform(0.6, 1.0), 2)
                })
            
            # Extract adverbs
            adverbs = self.extract_words_from_text(job_text, self.adverbs)
            for adv in set(adverbs):
                frequency = adverbs.count(adv)
                
                adverbs_data.append({
                    'id': len(adverbs_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'adverb': adv,
                    'context': 'work_style',
                    'frequency': frequency,
                    'intensity_score': round(random.uniform(0.6, 1.0), 2)
                })
        
        return (pd.DataFrame(primary_skills_data), pd.DataFrame(secondary_skills_data),
                pd.DataFrame(adjectives_data), pd.DataFrame(adverbs_data))

    def create_candidate_skills_data(self, limit=100):
        """Extract skills from candidate resumes"""
        primary_skills_data = []
        secondary_skills_data = []
        adjectives_data = []
        adverbs_data = []
        candidates_data = []
        
        for idx, row in self.resumes_df.iterrows():
            if idx >= limit:  # Limit for performance
                break
                
            candidate_id = f"CAND{str(idx+1).zfill(3)}"
            resume_text = str(row['Resume'])
            category = str(row['Category'])
            
            # Create candidate record
            candidates_data.append({
                'candidate_id': candidate_id,
                'name': f"Candidate {idx+1}",
                'email': f"candidate{idx+1}@email.com",
                'phone': f"090{random.randint(1000000, 9999999)}",
                'experience_years': random.randint(1, 15),
                'education_level': random.choice(['Bachelor', 'Master', 'PhD']),
                'current_position': category,
                'summary': resume_text[:200] + "..." if len(resume_text) > 200 else resume_text,
                'cv_text': resume_text
            })
            
            # Extract primary skills
            primary_skills = self.extract_skills_from_text(resume_text, self.primary_skills)
            for skill in set(primary_skills):
                frequency = primary_skills.count(skill)
                experience_years = random.randint(1, 8)
                
                primary_skills_data.append({
                    'id': len(primary_skills_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'skill_name': skill,
                    'skill_level': random.choice(['Beginner', 'Intermediate', 'Advanced', 'Expert']),
                    'years_experience': experience_years,
                    'importance_weight': 'NA',
                    'skill_category': self.categorize_skill(skill),
                    'frequency': frequency
                })
            
            # Extract secondary skills
            secondary_skills = self.extract_skills_from_text(resume_text, self.secondary_skills)
            for skill in set(secondary_skills):
                frequency = secondary_skills.count(skill)
                
                secondary_skills_data.append({
                    'id': len(secondary_skills_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'skill_name': skill,
                    'proficiency_level': random.choice(['Basic', 'Good', 'Advanced', 'Expert']),
                    'importance_weight': 'NA',
                    'skill_category': self.categorize_skill(skill),
                    'frequency': frequency
                })
            
            # Extract adjectives
            adjectives = self.extract_words_from_text(resume_text, self.adjectives)
            for adj in set(adjectives):
                frequency = adjectives.count(adj)
                
                adjectives_data.append({
                    'id': len(adjectives_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'adjective': adj,
                    'context': 'self_description',
                    'frequency': frequency,
                    'sentiment_score': round(random.uniform(0.6, 1.0), 2)
                })
            
            # Extract adverbs
            adverbs = self.extract_words_from_text(resume_text, self.adverbs)
            for adv in set(adverbs):
                frequency = adverbs.count(adv)
                
                adverbs_data.append({
                    'id': len(adverbs_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'adverb': adv,
                    'context': 'work_experience',
                    'frequency': frequency,
                    'intensity_score': round(random.uniform(0.6, 1.0), 2)
                })
        
        return (pd.DataFrame(primary_skills_data), pd.DataFrame(secondary_skills_data),
                pd.DataFrame(adjectives_data), pd.DataFrame(adverbs_data),
                pd.DataFrame(candidates_data))

    def categorize_skill(self, skill):
        """Categorize skills into different categories"""
        skill_lower = skill.lower()
        
        if skill_lower in ['python', 'java', 'javascript', 'typescript', 'php', 'nodejs']:
            return 'Programming Language'
        elif skill_lower in ['ai', 'machine learning']:
            return 'AI/ML'
        elif skill_lower in ['react', 'laravel', 'html5']:
            return 'Framework'
        elif skill_lower in ['sql', 'postgresql']:
            return 'Database'
        elif skill_lower in ['aws', 'gcp', 'azure', 'git', 'jenkins']:
            return 'DevOps/Cloud'
        else:
            return 'Other'

    def jaccard_similarity(self, set1, set2):
        """Calculate Jaccard similarity with weighted frequency"""
        if len(set1) == 0 and len(set2) == 0:
            return 1.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0

    def create_job_candidate_pairs(self, job_skills_dfs, candidate_skills_dfs):
        """Create job-candidate pairs with improved similarity calculation"""
        job_primary, job_secondary, job_adj, job_adv = job_skills_dfs
        cand_primary, cand_secondary, cand_adj, cand_adv, candidates = candidate_skills_dfs
        
        pairs_data = []
        
        # Get unique job and candidate IDs
        job_ids = job_primary['entity_id'].unique()
        candidate_ids = candidates['candidate_id'].unique()
        
        pair_id = 1
        
        for job_id in job_ids:
            # Select random candidates for this job (5 per job)
            selected_candidates = random.sample(list(candidate_ids), min(5, len(candidate_ids)))
            
            for candidate_id in selected_candidates:
                # Get skills with frequency weighting
                job_primary_skills = set(job_primary[job_primary['entity_id'] == job_id]['skill_name'])
                job_secondary_skills = set(job_secondary[job_secondary['entity_id'] == job_id]['skill_name'])
                job_adjectives = set(job_adj[job_adj['entity_id'] == job_id]['adjective'])
                job_adverbs = set(job_adv[job_adv['entity_id'] == job_id]['adverb'])
                
                cand_primary_skills = set(cand_primary[cand_primary['entity_id'] == candidate_id]['skill_name'])
                cand_secondary_skills = set(cand_secondary[cand_secondary['entity_id'] == candidate_id]['skill_name'])
                cand_adjectives = set(cand_adj[cand_adj['entity_id'] == candidate_id]['adjective'])
                cand_adverbs = set(cand_adv[cand_adv['entity_id'] == candidate_id]['adverb'])
                
                # Calculate Jaccard similarities
                primary_jaccard = self.jaccard_similarity(job_primary_skills, cand_primary_skills)
                secondary_jaccard = self.jaccard_similarity(job_secondary_skills, cand_secondary_skills)
                adjectives_jaccard = self.jaccard_similarity(job_adjectives, cand_adjectives)
                adverbs_jaccard = self.jaccard_similarity(job_adverbs, cand_adverbs)
                
                # Calculate overall suitability with improved weighting
                overall_suitability = (0.4 * primary_jaccard + 0.3 * secondary_jaccard + 
                                     0.15 * adjectives_jaccard + 0.15 * adverbs_jaccard)
                
                # Create suitability label with improved balanced thresholds
                # Based on 33/67 percentiles for better class distribution
                if overall_suitability >= 0.101:
                    suitability_label = 2  # Highly Suitable
                elif overall_suitability >= 0.033:
                    suitability_label = 1  # Moderately Suitable
                else:
                    suitability_label = 0  # Not Suitable
                
                # Create pair record
                pairs_data.append({
                    'pair_id': pair_id,
                    'job_id': job_id,
                    'candidate_id': candidate_id,
                    'primary_skills_jaccard': round(primary_jaccard, 3),
                    'secondary_skills_jaccard': round(secondary_jaccard, 3),
                    'adjectives_jaccard': round(adjectives_jaccard, 3),
                    'adverbs_jaccard': round(adverbs_jaccard, 3),
                    'overall_suitability': round(overall_suitability, 3),
                    'suitability_label': suitability_label,
                    'job_primary_count': len(job_primary_skills),
                    'candidate_primary_count': len(cand_primary_skills),
                    'primary_intersection': len(job_primary_skills.intersection(cand_primary_skills)),
                    'job_secondary_count': len(job_secondary_skills),
                    'candidate_secondary_count': len(cand_secondary_skills),
                    'secondary_intersection': len(job_secondary_skills.intersection(cand_secondary_skills))
                })
                
                pair_id += 1
        
        return pd.DataFrame(pairs_data)

    def run_complete_pipeline(self):
        """Run the improved pipeline"""
        print("STARTING IMPROVED AI JOB MATCHING PIPELINE")
        print("=" * 60)
        
        # Create output directory
        os.makedirs('data/improved', exist_ok=True)
        
        # Step 1: Load data
        print("\n1. Loading data...")
        if not self.load_data():
            return False
        
        # Step 2: Extract job skills
        print("\n2. Extracting skills from job descriptions...")
        job_skills_dfs = self.create_job_skills_data()
        
        # Step 3: Extract candidate skills
        print("\n3. Extracting skills from candidate resumes...")
        candidate_skills_dfs = self.create_candidate_skills_data()
        
        # Step 4: Create job-candidate pairs
        print("\n4. Creating job-candidate pairs...")
        pairs_df = self.create_job_candidate_pairs(job_skills_dfs, candidate_skills_dfs)
        
        # Step 5: Save all data
        print("\n5. Saving improved data...")
        
        # Save job skills
        job_primary, job_secondary, job_adj, job_adv = job_skills_dfs
        job_primary.to_csv('data/improved/job_primary_skills.csv', index=False)
        job_secondary.to_csv('data/improved/job_secondary_skills.csv', index=False)
        job_adj.to_csv('data/improved/job_adjectives.csv', index=False)
        job_adv.to_csv('data/improved/job_adverbs.csv', index=False)
        
        # Save candidate skills
        cand_primary, cand_secondary, cand_adj, cand_adv, candidates = candidate_skills_dfs
        cand_primary.to_csv('data/improved/candidate_primary_skills.csv', index=False)
        cand_secondary.to_csv('data/improved/candidate_secondary_skills.csv', index=False)
        cand_adj.to_csv('data/improved/candidate_adjectives.csv', index=False)
        cand_adv.to_csv('data/improved/candidate_adverbs.csv', index=False)
        candidates.to_csv('data/improved/candidates.csv', index=False)
        
        # Save pairs
        pairs_df.to_csv('data/improved/job_candidate_pairs.csv', index=False)
        
        # Print summary
        print("\n" + "=" * 60)
        print("IMPROVED PIPELINE COMPLETED!")
        print("=" * 60)
        
        print(f"\nUsing REAL EXTRACTED SKILLS:")
        print(f"Job Skills:")
        print(f"   - Primary skills: {len(job_primary)} records")
        print(f"   - Secondary skills: {len(job_secondary)} records")
        print(f"   - Adjectives: {len(job_adj)} records")
        print(f"   - Adverbs: {len(job_adv)} records")
        
        print(f"\nCandidate Skills:")
        print(f"   - Primary skills: {len(cand_primary)} records")
        print(f"   - Secondary skills: {len(cand_secondary)} records")
        print(f"   - Adjectives: {len(cand_adj)} records")
        print(f"   - Adverbs: {len(cand_adv)} records")
        
        print(f"\nJob-Candidate Pairs: {len(pairs_df)} records")
        
        # Label distribution
        label_dist = pairs_df['suitability_label'].value_counts().sort_index()
        print(f"\nImproved Suitability Distribution:")
        print(f"   Not Suitable (0): {label_dist.get(0, 0)}")
        print(f"   Moderately Suitable (1): {label_dist.get(1, 0)}")
        print(f"   Highly Suitable (2): {label_dist.get(2, 0)}")
        
        # Show top matches
        if len(pairs_df) > 0:
            top_matches = pairs_df.nlargest(5, 'overall_suitability')
            print(f"\nTOP 5 MATCHES:")
            for _, match in top_matches.iterrows():
                print(f"   {match['job_id']} + {match['candidate_id']}: {match['overall_suitability']:.3f} (Label: {match['suitability_label']})")
        
        print(f"\nNext Steps:")
        print(f"   1. Use data/improved/job_candidate_pairs.csv for ML training")
        print(f"   2. Compare results with previous hard-coded approach")
        print(f"   3. Train models with improved features")
        
        return True

if __name__ == "__main__":
    pipeline = ImprovedJobMatchingPipeline()
    pipeline.run_complete_pipeline()
