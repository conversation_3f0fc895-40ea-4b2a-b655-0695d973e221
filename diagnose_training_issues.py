#!/usr/bin/env python3
"""
Chẩn đ<PERSON><PERSON> các vấn đề trong training
Kiểm tra data leakage, overfitting, và đề xuất gi<PERSON>i pháp
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

class TrainingDiagnostics:
    def __init__(self):
        self.data = None
        
    def load_data(self):
        """Load training data"""
        try:
            self.data = pd.read_csv('data/improved/job_candidate_pairs.csv')
            print(f"Loaded {len(self.data)} job-candidate pairs")
            return True
        except FileNotFoundError:
            print("Error: Data file not found")
            return False
    
    def check_data_leakage(self):
        """Kiểm tra data leakage"""
        print("\n" + "="*60)
        print("1. DATA LEAKAGE ANALYSIS")
        print("="*60)
        
        # Check correlation between features and target
        feature_cols = [
            'primary_skills_jaccard', 'secondary_skills_jaccard', 
            'adjectives_jaccard', 'adverbs_jaccard', 'overall_suitability'
        ]
        
        correlations = []
        for col in feature_cols:
            if col in self.data.columns:
                corr = self.data[col].corr(self.data['suitability_label'])
                correlations.append((col, corr))
                print(f"  {col:<25}: {corr:.4f} correlation with target")
        
        # Check if overall_suitability is perfectly correlated
        if 'overall_suitability' in self.data.columns:
            perfect_corr = self.data['overall_suitability'].corr(self.data['suitability_label'])
            if perfect_corr > 0.95:
                print(f"\n🚨 SEVERE DATA LEAKAGE DETECTED!")
                print(f"   overall_suitability has {perfect_corr:.4f} correlation with target")
                print(f"   This means the target was created FROM the features!")
        
        return correlations
    
    def check_threshold_logic(self):
        """Kiểm tra logic threshold"""
        print("\n" + "="*60)
        print("2. THRESHOLD LOGIC ANALYSIS")
        print("="*60)
        
        if 'overall_suitability' not in self.data.columns:
            print("overall_suitability column not found")
            return
        
        # Analyze how labels were created
        for label in [0, 1, 2]:
            subset = self.data[self.data['suitability_label'] == label]
            if len(subset) > 0:
                min_score = subset['overall_suitability'].min()
                max_score = subset['overall_suitability'].max()
                mean_score = subset['overall_suitability'].mean()
                
                print(f"  Label {label}: score range {min_score:.3f} - {max_score:.3f} (avg: {mean_score:.3f})")
        
        # Check for clear thresholds
        sorted_data = self.data.sort_values('overall_suitability')
        
        print(f"\n  Threshold Analysis:")
        label_changes = []
        for i in range(1, len(sorted_data)):
            prev_label = sorted_data.iloc[i-1]['suitability_label']
            curr_label = sorted_data.iloc[i]['suitability_label']
            if prev_label != curr_label:
                threshold = sorted_data.iloc[i]['overall_suitability']
                label_changes.append((prev_label, curr_label, threshold))
                print(f"    Label changes from {prev_label} to {curr_label} at score {threshold:.3f}")
        
        if len(label_changes) <= 2:
            print(f"\n🚨 SIMPLE THRESHOLD PROBLEM!")
            print(f"   Labels are created by simple thresholds on overall_suitability")
            print(f"   Any model can achieve 100% by learning these thresholds!")
    
    def check_dataset_size(self):
        """Kiểm tra kích thước dataset"""
        print("\n" + "="*60)
        print("3. DATASET SIZE ANALYSIS")
        print("="*60)
        
        total_samples = len(self.data)
        label_counts = self.data['suitability_label'].value_counts().sort_index()
        
        print(f"  Total samples: {total_samples}")
        print(f"  Samples per class:")
        for label in [0, 1, 2]:
            count = label_counts.get(label, 0)
            print(f"    Class {label}: {count} samples")
        
        # Check if dataset is too small
        min_samples_per_class = min(label_counts)
        
        if total_samples < 1000:
            print(f"\n⚠️  SMALL DATASET WARNING!")
            print(f"   {total_samples} samples is quite small for ML")
            print(f"   Minimum class has only {min_samples_per_class} samples")
            print(f"   Risk of overfitting is very high!")
        
        # Estimate train/val/test split
        train_size = int(total_samples * 0.6)
        val_size = int(total_samples * 0.2)
        test_size = total_samples - train_size - val_size
        
        print(f"\n  With 60/20/20 split:")
        print(f"    Train: {train_size} samples")
        print(f"    Validation: {val_size} samples") 
        print(f"    Test: {test_size} samples")
        
        if test_size < 50:
            print(f"\n🚨 TEST SET TOO SMALL!")
            print(f"   {test_size} test samples is insufficient for reliable evaluation")
    
    def test_simple_model(self):
        """Test với model đơn giản để kiểm tra overfitting"""
        print("\n" + "="*60)
        print("4. SIMPLE MODEL TEST")
        print("="*60)
        
        # Prepare features (exclude overall_suitability to avoid leakage)
        feature_cols = [
            'primary_skills_jaccard', 'secondary_skills_jaccard', 
            'adjectives_jaccard', 'adverbs_jaccard'
        ]
        
        X = self.data[feature_cols]
        y = self.data['suitability_label']
        
        # Test with different train sizes
        train_sizes = [0.5, 0.6, 0.7, 0.8, 0.9]
        
        print(f"  Testing Decision Tree with different train sizes:")
        
        for train_size in train_sizes:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, train_size=train_size, random_state=42, stratify=y
            )
            
            # Simple decision tree
            model = DecisionTreeClassifier(max_depth=3, random_state=42)
            model.fit(X_train, y_train)
            
            train_acc = accuracy_score(y_train, model.predict(X_train))
            test_acc = accuracy_score(y_test, model.predict(X_test))
            
            print(f"    Train size {train_size:.1f}: Train {train_acc:.3f}, Test {test_acc:.3f}, Diff {abs(train_acc-test_acc):.3f}")
    
    def suggest_solutions(self):
        """Đề xuất giải pháp"""
        print("\n" + "="*60)
        print("5. SUGGESTED SOLUTIONS")
        print("="*60)
        
        print(f"🔧 IMMEDIATE FIXES:")
        print(f"  1. Remove 'overall_suitability' from features (data leakage)")
        print(f"  2. Add more diverse features:")
        print(f"     - TF-IDF similarity between job description and resume")
        print(f"     - Experience level matching")
        print(f"     - Location compatibility")
        print(f"     - Salary range matching")
        print(f"  3. Increase dataset size:")
        print(f"     - Generate more job-candidate pairs")
        print(f"     - Use data augmentation techniques")
        print(f"     - Collect real-world matching data")
        
        print(f"\n🎯 BETTER EVALUATION:")
        print(f"  1. Use cross-validation instead of single split")
        print(f"  2. Add noise to test model robustness")
        print(f"  3. Test on completely unseen data")
        print(f"  4. Use more realistic evaluation metrics")
        
        print(f"\n📊 REALISTIC EXPECTATIONS:")
        print(f"  1. 100% accuracy is unrealistic for real-world problems")
        print(f"  2. 80-90% accuracy is more reasonable for job matching")
        print(f"  3. Focus on precision/recall for each class")
        print(f"  4. Consider business impact of false positives/negatives")
    
    def create_improved_features(self):
        """Tạo features cải tiến không bị data leakage"""
        print("\n" + "="*60)
        print("6. CREATING IMPROVED FEATURES")
        print("="*60)
        
        # Load original data
        try:
            jobs_df = pd.read_csv('data/itviec_jobs_undetected.csv')
            resumes_df = pd.read_csv('data/UpdatedResumeDataSet.csv')
            
            print(f"  Creating TF-IDF based features...")
            
            # This would require implementing TF-IDF similarity
            # between job descriptions and resumes
            print(f"  ✅ Job descriptions loaded: {len(jobs_df)}")
            print(f"  ✅ Resumes loaded: {len(resumes_df)}")
            print(f"  💡 Next step: Implement TF-IDF similarity features")
            
        except FileNotFoundError:
            print(f"  ❌ Original data files not found")
    
    def run_full_diagnosis(self):
        """Chạy chẩn đoán đầy đủ"""
        print("TRAINING ISSUES DIAGNOSIS")
        print("="*60)
        
        if not self.load_data():
            return
        
        # Run all checks
        self.check_data_leakage()
        self.check_threshold_logic()
        self.check_dataset_size()
        self.test_simple_model()
        self.suggest_solutions()
        self.create_improved_features()
        
        print(f"\n" + "="*60)
        print("DIAGNOSIS COMPLETED")
        print("="*60)
        
        print(f"\n🎯 SUMMARY:")
        print(f"  ❌ Data leakage detected (overall_suitability → target)")
        print(f"  ❌ Dataset too small (100 samples)")
        print(f"  ❌ Simple threshold logic makes problem trivial")
        print(f"  ❌ 100% accuracy is unrealistic and indicates overfitting")
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"  Create a more realistic dataset with:")
        print(f"  - More samples (1000+)")
        print(f"  - Independent features (no data leakage)")
        print(f"  - Real-world complexity")
        print(f"  - Proper evaluation methodology")

def main():
    """Main function"""
    diagnostics = TrainingDiagnostics()
    diagnostics.run_full_diagnosis()

if __name__ == "__main__":
    main()
