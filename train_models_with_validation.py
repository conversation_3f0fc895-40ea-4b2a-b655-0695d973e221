#!/usr/bin/env python3
"""
Enhanced Model Training với Train/Validation/Test Split
Chia dữ liệu thành 3 tập: Train (60%), Validation (20%), Test (20%)
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.linear_model import LinearRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import AdaBoostClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

class EnhancedModelTrainer:
    def __init__(self):
        self.data = None
        self.X_train = None
        self.X_val = None
        self.X_test = None
        self.y_train = None
        self.y_val = None
        self.y_test = None
        self.models = {}
        self.results = []

    def load_data(self):
        """Load training data"""
        try:
            # Try improved data first
            try:
                self.data = pd.read_csv('data/improved/job_candidate_pairs.csv')
                print(f"Loaded {len(self.data)} job-candidate pairs from improved dataset")
            except FileNotFoundError:
                self.data = pd.read_csv('data/job_candidate_pairs.csv')
                print(f"Loaded {len(self.data)} job-candidate pairs from basic dataset")
            
            print(f"Data shape: {self.data.shape}")
            
            # Show label distribution
            label_counts = self.data['suitability_label'].value_counts().sort_index()
            total = len(self.data)
            print(f"\nLabel Distribution:")
            labels = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}
            for label in [0, 1, 2]:
                count = label_counts.get(label, 0)
                percentage = (count / total) * 100
                print(f"  Class {label} ({labels[label]}): {count} samples ({percentage:.1f}%)")
            
            return True
            
        except FileNotFoundError:
            print("Error: job_candidate_pairs.csv not found!")
            print("Please run improved_data_pipeline.py first")
            return False

    def prepare_features(self):
        """Prepare feature matrix and target vector"""
        # Select feature columns
        feature_cols = [
            'primary_skills_jaccard', 'secondary_skills_jaccard', 
            'adjectives_jaccard', 'adverbs_jaccard'
        ]
        
        X = self.data[feature_cols].copy()
        y = self.data['suitability_label']
        
        # Add engineered features
        X['total_similarity'] = (X['primary_skills_jaccard'] + X['secondary_skills_jaccard'] + 
                                X['adjectives_jaccard'] + X['adverbs_jaccard'])
        
        # Weighted similarity (based on research paper weights)
        X['weighted_similarity'] = (0.4 * X['primary_skills_jaccard'] + 
                                   0.3 * X['secondary_skills_jaccard'] + 
                                   0.15 * X['adjectives_jaccard'] + 
                                   0.15 * X['adverbs_jaccard'])
        
        # Interaction features
        X['primary_secondary_interaction'] = X['primary_skills_jaccard'] * X['secondary_skills_jaccard']
        X['adj_adv_interaction'] = X['adjectives_jaccard'] * X['adverbs_jaccard']
        
        print(f"Feature matrix shape: {X.shape}")
        print(f"Features: {list(X.columns)}")
        
        return X, y

    def split_data(self, train_size=0.6, val_size=0.2, test_size=0.2, random_state=42):
        """Split data into train/validation/test sets"""
        X, y = self.prepare_features()
        
        # Verify split ratios
        assert abs(train_size + val_size + test_size - 1.0) < 1e-6, "Split ratios must sum to 1.0"
        
        # First split: separate test set
        X_temp, self.X_test, y_temp, self.y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        # Second split: separate train and validation from remaining data
        val_ratio = val_size / (train_size + val_size)  # Adjust validation ratio
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            X_temp, y_temp, test_size=val_ratio, random_state=random_state, stratify=y_temp
        )
        
        print(f"\nData Split:")
        print(f"  Training set: {len(self.X_train)} samples ({len(self.X_train)/len(X)*100:.1f}%)")
        print(f"  Validation set: {len(self.X_val)} samples ({len(self.X_val)/len(X)*100:.1f}%)")
        print(f"  Test set: {len(self.X_test)} samples ({len(self.X_test)/len(X)*100:.1f}%)")
        
        # Show label distribution for each set
        print(f"\nLabel Distribution by Set:")
        for set_name, y_set in [('Train', self.y_train), ('Validation', self.y_val), ('Test', self.y_test)]:
            dist = y_set.value_counts().sort_index()
            dist_str = ', '.join([f"{label}: {dist.get(label, 0)}" for label in [0, 1, 2]])
            print(f"  {set_name}: {dist_str}")

    def train_linear_regression(self):
        """Train Linear Regression model"""
        print(f"\nTraining Linear Regression...")
        
        model = LinearRegression()
        model.fit(self.X_train, self.y_train)
        
        # Predictions
        train_pred = model.predict(self.X_train)
        val_pred = model.predict(self.X_val)
        test_pred = model.predict(self.X_test)
        
        # Convert to classification (round to nearest integer)
        train_pred_class = np.round(np.clip(train_pred, 0, 2)).astype(int)
        val_pred_class = np.round(np.clip(val_pred, 0, 2)).astype(int)
        test_pred_class = np.round(np.clip(test_pred, 0, 2)).astype(int)
        
        # Calculate accuracies
        train_acc = accuracy_score(self.y_train, train_pred_class)
        val_acc = accuracy_score(self.y_val, val_pred_class)
        test_acc = accuracy_score(self.y_test, test_pred_class)
        
        print(f"  Train Accuracy: {train_acc:.4f}")
        print(f"  Validation Accuracy: {val_acc:.4f}")
        print(f"  Test Accuracy: {test_acc:.4f}")
        
        self.models['Linear Regression'] = model
        self.results.append({
            'Model': 'Linear Regression',
            'Train_Accuracy': train_acc,
            'Val_Accuracy': val_acc,
            'Test_Accuracy': test_acc,
            'Train_Val_Diff': abs(train_acc - val_acc),
            'Val_Test_Diff': abs(val_acc - test_acc)
        })
        
        return model, test_pred_class

    def train_decision_tree(self):
        """Train Decision Tree with validation-based hyperparameter tuning"""
        print(f"\nTraining Decision Tree...")
        
        # Hyperparameter grid
        param_grid = {
            'max_depth': [3, 5, 7, 10, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        # Use validation set for hyperparameter tuning
        best_score = 0
        best_params = None
        best_model = None
        
        for max_depth in param_grid['max_depth']:
            for min_samples_split in param_grid['min_samples_split']:
                for min_samples_leaf in param_grid['min_samples_leaf']:
                    model = DecisionTreeClassifier(
                        max_depth=max_depth,
                        min_samples_split=min_samples_split,
                        min_samples_leaf=min_samples_leaf,
                        random_state=42
                    )
                    
                    model.fit(self.X_train, self.y_train)
                    val_pred = model.predict(self.X_val)
                    val_score = accuracy_score(self.y_val, val_pred)
                    
                    if val_score > best_score:
                        best_score = val_score
                        best_params = {
                            'max_depth': max_depth,
                            'min_samples_split': min_samples_split,
                            'min_samples_leaf': min_samples_leaf
                        }
                        best_model = model
        
        print(f"  Best params: {best_params}")
        print(f"  Best validation score: {best_score:.4f}")
        
        # Evaluate on all sets
        train_pred = best_model.predict(self.X_train)
        val_pred = best_model.predict(self.X_val)
        test_pred = best_model.predict(self.X_test)
        
        train_acc = accuracy_score(self.y_train, train_pred)
        val_acc = accuracy_score(self.y_val, val_pred)
        test_acc = accuracy_score(self.y_test, test_pred)
        
        print(f"  Train Accuracy: {train_acc:.4f}")
        print(f"  Validation Accuracy: {val_acc:.4f}")
        print(f"  Test Accuracy: {test_acc:.4f}")
        
        self.models['Decision Tree'] = best_model
        self.results.append({
            'Model': 'Decision Tree',
            'Train_Accuracy': train_acc,
            'Val_Accuracy': val_acc,
            'Test_Accuracy': test_acc,
            'Train_Val_Diff': abs(train_acc - val_acc),
            'Val_Test_Diff': abs(val_acc - test_acc)
        })
        
        return best_model, test_pred

    def train_adaboost(self):
        """Train AdaBoost with validation-based hyperparameter tuning"""
        print(f"\nTraining AdaBoost...")
        
        # Hyperparameter grid
        param_grid = {
            'n_estimators': [50, 100, 200],
            'learning_rate': [0.01, 0.1, 1.0],
            'algorithm': ['SAMME', 'SAMME.R']
        }
        
        best_score = 0
        best_params = None
        best_model = None
        
        for n_estimators in param_grid['n_estimators']:
            for learning_rate in param_grid['learning_rate']:
                for algorithm in param_grid['algorithm']:
                    try:
                        model = AdaBoostClassifier(
                            n_estimators=n_estimators,
                            learning_rate=learning_rate,
                            algorithm=algorithm,
                            random_state=42
                        )
                        
                        model.fit(self.X_train, self.y_train)
                        val_pred = model.predict(self.X_val)
                        val_score = accuracy_score(self.y_val, val_pred)
                        
                        if val_score > best_score:
                            best_score = val_score
                            best_params = {
                                'n_estimators': n_estimators,
                                'learning_rate': learning_rate,
                                'algorithm': algorithm
                            }
                            best_model = model
                    except:
                        continue
        
        print(f"  Best params: {best_params}")
        print(f"  Best validation score: {best_score:.4f}")
        
        # Evaluate on all sets
        train_pred = best_model.predict(self.X_train)
        val_pred = best_model.predict(self.X_val)
        test_pred = best_model.predict(self.X_test)
        
        train_acc = accuracy_score(self.y_train, train_pred)
        val_acc = accuracy_score(self.y_val, val_pred)
        test_acc = accuracy_score(self.y_test, test_pred)
        
        print(f"  Train Accuracy: {train_acc:.4f}")
        print(f"  Validation Accuracy: {val_acc:.4f}")
        print(f"  Test Accuracy: {test_acc:.4f}")
        
        self.models['AdaBoost'] = best_model
        self.results.append({
            'Model': 'AdaBoost',
            'Train_Accuracy': train_acc,
            'Val_Accuracy': val_acc,
            'Test_Accuracy': test_acc,
            'Train_Val_Diff': abs(train_acc - val_acc),
            'Val_Test_Diff': abs(val_acc - test_acc)
        })
        
        return best_model, test_pred

    def train_xgboost(self):
        """Train XGBoost with validation-based hyperparameter tuning"""
        print(f"\nTraining XGBoost...")
        
        # Hyperparameter grid (smaller for faster training)
        param_grid = {
            'n_estimators': [50, 100],
            'max_depth': [3, 5],
            'learning_rate': [0.01, 0.1],
            'subsample': [0.8, 1.0],
            'colsample_bytree': [0.8, 1.0]
        }
        
        best_score = 0
        best_params = None
        best_model = None
        
        for n_estimators in param_grid['n_estimators']:
            for max_depth in param_grid['max_depth']:
                for learning_rate in param_grid['learning_rate']:
                    for subsample in param_grid['subsample']:
                        for colsample_bytree in param_grid['colsample_bytree']:
                            model = xgb.XGBClassifier(
                                n_estimators=n_estimators,
                                max_depth=max_depth,
                                learning_rate=learning_rate,
                                subsample=subsample,
                                colsample_bytree=colsample_bytree,
                                random_state=42,
                                eval_metric='mlogloss'
                            )
                            
                            model.fit(self.X_train, self.y_train)
                            val_pred = model.predict(self.X_val)
                            val_score = accuracy_score(self.y_val, val_pred)
                            
                            if val_score > best_score:
                                best_score = val_score
                                best_params = {
                                    'n_estimators': n_estimators,
                                    'max_depth': max_depth,
                                    'learning_rate': learning_rate,
                                    'subsample': subsample,
                                    'colsample_bytree': colsample_bytree
                                }
                                best_model = model
        
        print(f"  Best params: {best_params}")
        print(f"  Best validation score: {best_score:.4f}")
        
        # Evaluate on all sets
        train_pred = best_model.predict(self.X_train)
        val_pred = best_model.predict(self.X_val)
        test_pred = best_model.predict(self.X_test)
        
        train_acc = accuracy_score(self.y_train, train_pred)
        val_acc = accuracy_score(self.y_val, val_pred)
        test_acc = accuracy_score(self.y_test, test_pred)
        
        print(f"  Train Accuracy: {train_acc:.4f}")
        print(f"  Validation Accuracy: {val_acc:.4f}")
        print(f"  Test Accuracy: {test_acc:.4f}")
        
        # Feature importance
        feature_importance = best_model.feature_importances_
        feature_names = self.X_train.columns
        
        print(f"\n  Feature Importance:")
        for name, importance in zip(feature_names, feature_importance):
            print(f"    {name}: {importance:.4f}")
        
        self.models['XGBoost'] = best_model
        self.results.append({
            'Model': 'XGBoost',
            'Train_Accuracy': train_acc,
            'Val_Accuracy': val_acc,
            'Test_Accuracy': test_acc,
            'Train_Val_Diff': abs(train_acc - val_acc),
            'Val_Test_Diff': abs(val_acc - test_acc)
        })
        
        return best_model, test_pred

    def train_all_models(self):
        """Train all models"""
        print(f"\nTRAINING ALL MODELS WITH VALIDATION")
        print("="*60)
        
        # Train models
        self.train_linear_regression()
        self.train_decision_tree()
        self.train_adaboost()
        self.train_xgboost()

    def compare_models(self):
        """Compare all models"""
        print(f"\n" + "="*80)
        print("MODEL COMPARISON WITH VALIDATION")
        print("="*80)
        
        # Create results DataFrame
        results_df = pd.DataFrame(self.results)
        
        # Display results
        print(results_df.to_string(index=False, float_format='%.4f'))
        
        # Find best model based on validation accuracy
        best_val_idx = results_df['Val_Accuracy'].idxmax()
        best_model_name = results_df.loc[best_val_idx, 'Model']
        best_val_accuracy = results_df.loc[best_val_idx, 'Val_Accuracy']
        best_test_accuracy = results_df.loc[best_val_idx, 'Test_Accuracy']
        
        print(f"\nBest Model (by validation): {best_model_name}")
        print(f"Validation Accuracy: {best_val_accuracy:.4f} ({best_val_accuracy*100:.2f}%)")
        print(f"Test Accuracy: {best_test_accuracy:.4f} ({best_test_accuracy*100:.2f}%)")
        
        # Check for overfitting
        print(f"\nOverfitting Analysis:")
        for _, row in results_df.iterrows():
            train_val_diff = row['Train_Val_Diff']
            val_test_diff = row['Val_Test_Diff']
            
            if train_val_diff > 0.1:
                overfitting_status = "HIGH overfitting"
            elif train_val_diff > 0.05:
                overfitting_status = "MODERATE overfitting"
            else:
                overfitting_status = "LOW overfitting"
            
            print(f"  {row['Model']}: {overfitting_status} (Train-Val: {train_val_diff:.3f}, Val-Test: {val_test_diff:.3f})")
        
        # Target comparison
        target_accuracy = 0.9514
        if best_test_accuracy >= target_accuracy:
            print(f"\nTARGET ACHIEVED! Test Accuracy >= {target_accuracy*100:.2f}%")
        else:
            print(f"\nTarget not reached. Need {target_accuracy*100:.2f}%, got {best_test_accuracy*100:.2f}%")
        
        # Save results
        results_df.to_csv('data/model_results_with_validation.csv', index=False)
        print(f"\nResults saved to data/model_results_with_validation.csv")
        
        return best_model_name, best_test_accuracy

    def run_complete_training(self):
        """Run complete training pipeline with validation"""
        print("ENHANCED MODEL TRAINING WITH VALIDATION")
        print("="*60)
        
        # Load data
        if not self.load_data():
            return False
        
        # Split data into train/val/test
        print(f"\n1. Splitting data into train/validation/test...")
        self.split_data()
        
        # Train models
        print(f"\n2. Training models with validation-based hyperparameter tuning...")
        self.train_all_models()
        
        # Compare models
        print(f"\n3. Comparing models...")
        best_model_name, best_accuracy = self.compare_models()
        
        print(f"\nENHANCED MODEL TRAINING COMPLETED!")
        return True

def main():
    """Main function"""
    trainer = EnhancedModelTrainer()
    trainer.run_complete_training()

if __name__ == "__main__":
    main()
