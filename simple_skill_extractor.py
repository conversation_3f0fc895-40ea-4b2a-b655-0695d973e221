#!/usr/bin/env python3
"""
Simple Skill Extractor - Tự động trích xuất skills từ dữ liệu thực tế
Không cần NLTK, sử dụng regex và TF-IDF
"""

import pandas as pd
import numpy as np
import re
from collections import Counter
from sklearn.feature_extraction.text import TfidfVectorizer

class SimpleSkillExtractor:
    def __init__(self):
        # Common stop words
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those',
            'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
        }
        
        # Non-skill words to filter out
        self.non_skills = {
            'experience', 'years', 'work', 'team', 'project', 'company', 'role', 'position',
            'job', 'career', 'professional', 'business', 'industry', 'client', 'customer',
            'time', 'day', 'month', 'year', 'good', 'great', 'excellent', 'strong',
            'knowledge', 'understanding', 'ability', 'skill', 'skills', 'technology',
            'development', 'developer', 'engineer', 'manager', 'analyst', 'specialist',
            'working', 'using', 'including', 'such', 'well', 'also', 'new', 'high',
            'level', 'based', 'related', 'various', 'different', 'multiple', 'several'
        }

    def clean_text(self, text):
        """Clean and preprocess text"""
        if pd.isna(text):
            return ""
        
        text = str(text).lower()
        # Remove special characters but keep dots, plus, hash for tech terms
        text = re.sub(r'[^\w\s\.\+\#\-]', ' ', text)
        # Remove extra whitespaces
        text = ' '.join(text.split())
        return text

    def extract_skills_with_tfidf(self, texts, min_df=3, max_df=0.7, max_features=500):
        """Extract important terms using TF-IDF"""
        # Clean texts
        cleaned_texts = [self.clean_text(text) for text in texts if text]
        
        # Create TF-IDF vectorizer
        vectorizer = TfidfVectorizer(
            min_df=min_df,
            max_df=max_df,
            max_features=max_features,
            stop_words=list(self.stop_words),
            ngram_range=(1, 3),  # Include unigrams, bigrams, trigrams
            token_pattern=r'\b[a-zA-Z][a-zA-Z0-9\.\+\#\-]*[a-zA-Z0-9]\b|\b[a-zA-Z]\b'
        )
        
        try:
            # Fit and transform
            tfidf_matrix = vectorizer.fit_transform(cleaned_texts)
            feature_names = vectorizer.get_feature_names_out()
            
            # Get average TF-IDF scores
            mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)
            
            # Create skill candidates with scores
            skill_candidates = []
            for i, feature in enumerate(feature_names):
                if self.is_potential_skill(feature):
                    skill_candidates.append((feature, mean_scores[i]))
            
            # Sort by TF-IDF score
            skill_candidates.sort(key=lambda x: x[1], reverse=True)
            
            return skill_candidates
        except Exception as e:
            print(f"Error in TF-IDF extraction: {e}")
            return []

    def is_potential_skill(self, term):
        """Check if a term is potentially a skill"""
        term_lower = term.lower().strip()
        
        # Filter out empty or very short terms
        if len(term_lower) < 2:
            return False
        
        # Filter out common non-skill words
        if term_lower in self.non_skills or term_lower in self.stop_words:
            return False
        
        # Filter out terms that are mostly numbers
        if re.match(r'^\d+$', term_lower):
            return False
        
        # Keep terms that look like technical skills
        if any([
            # Programming languages
            term_lower in ['python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin', 'scala', 'r', 'matlab'],
            # Frameworks
            term_lower in ['react', 'angular', 'vue', 'nodejs', 'express', 'django', 'flask', 'spring', 'laravel', 'rails'],
            # Databases
            term_lower in ['sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'cassandra', 'oracle'],
            # Cloud & DevOps
            term_lower in ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'git', 'gitlab', 'github'],
            # AI/ML
            'machine learning' in term_lower or 'deep learning' in term_lower or term_lower in ['ai', 'tensorflow', 'pytorch', 'scikit', 'keras', 'opencv', 'nlp'],
            # Tools
            term_lower in ['jira', 'confluence', 'tableau', 'excel', 'photoshop', 'figma'],
            # Technical patterns
            re.match(r'^[a-z]+\.js$', term_lower),  # JavaScript frameworks
            re.match(r'^[a-z]+sql$', term_lower),   # SQL variants
            '+' in term_lower or '#' in term_lower,  # C++, C#
            term_lower.endswith(('api', 'sdk', 'ide', 'orm', 'cms')),
            # Version numbers
            re.search(r'\d+', term_lower) and len(term_lower) <= 10
        ]):
            return True
        
        # Keep compound technical terms
        if '-' in term_lower or '.' in term_lower:
            parts = re.split(r'[-.]', term_lower)
            if any(part in ['web', 'mobile', 'data', 'cloud', 'micro', 'full', 'front', 'back', 'end'] for part in parts):
                return True
        
        return False

    def extract_adjectives_simple(self, texts):
        """Extract adjectives using simple patterns"""
        # Common adjectives in job descriptions
        job_adjectives = {
            'innovative', 'creative', 'analytical', 'strategic', 'technical',
            'experienced', 'skilled', 'professional', 'collaborative', 'proactive',
            'dynamic', 'flexible', 'reliable', 'efficient', 'effective',
            'motivated', 'dedicated', 'passionate', 'detail-oriented', 'results-driven',
            'senior', 'junior', 'lead', 'principal', 'expert', 'advanced', 'strong',
            'excellent', 'outstanding', 'exceptional', 'proven', 'solid', 'extensive'
        }
        
        adjective_counts = Counter()
        
        for text in texts:
            if pd.isna(text):
                continue
            
            text_clean = self.clean_text(text)
            words = text_clean.split()
            
            for word in words:
                if word in job_adjectives:
                    adjective_counts[word] += 1
        
        return adjective_counts

    def extract_adverbs_simple(self, texts):
        """Extract adverbs using simple patterns"""
        # Common adverbs in job descriptions
        job_adverbs = {
            'efficiently', 'effectively', 'successfully', 'independently',
            'collaboratively', 'systematically', 'strategically', 'consistently',
            'professionally', 'rapidly', 'accurately', 'thoroughly',
            'creatively', 'analytically', 'proactively', 'quickly', 'carefully',
            'directly', 'closely', 'actively', 'regularly', 'continuously'
        }
        
        adverb_counts = Counter()
        
        for text in texts:
            if pd.isna(text):
                continue
            
            text_clean = self.clean_text(text)
            words = text_clean.split()
            
            for word in words:
                if word in job_adverbs:
                    adverb_counts[word] += 1
        
        return adverb_counts

    def categorize_skills(self, skills):
        """Categorize skills into primary and secondary"""
        primary_keywords = {
            'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
            'react', 'angular', 'vue', 'nodejs', 'django', 'flask', 'spring', 'laravel',
            'sql', 'mysql', 'postgresql', 'mongodb', 'redis',
            'machine learning', 'deep learning', 'ai', 'tensorflow', 'pytorch', 'scikit', 'keras',
            'html', 'css', 'bootstrap', 'jquery'
        }
        
        secondary_keywords = {
            'git', 'github', 'gitlab', 'docker', 'kubernetes', 'jenkins', 'aws', 'azure', 'gcp',
            'jira', 'confluence', 'agile', 'scrum', 'ci cd', 'linux', 'unix', 'windows',
            'communication', 'teamwork', 'leadership', 'problem solving', 'analytical thinking'
        }
        
        primary_skills = []
        secondary_skills = []
        
        for skill in skills:
            skill_lower = skill.lower()
            
            # Check if it's a primary skill
            is_primary = any(keyword in skill_lower for keyword in primary_keywords)
            
            if is_primary:
                primary_skills.append(skill)
            else:
                # Check if it's a secondary skill
                is_secondary = any(keyword in skill_lower for keyword in secondary_keywords)
                if is_secondary:
                    secondary_skills.append(skill)
                else:
                    # Default to secondary for unknown skills
                    secondary_skills.append(skill)
        
        return primary_skills, secondary_skills

    def extract_all_skills_from_data(self, jobs_file, resumes_file):
        """Extract all skills from job descriptions and resumes"""
        print("EXTRACTING SKILLS FROM REAL DATA")
        print("=" * 60)
        
        # Load data
        jobs_df = pd.read_csv(jobs_file)
        resumes_df = pd.read_csv(resumes_file)
        
        print(f"Loaded {len(jobs_df)} job descriptions")
        print(f"Loaded {len(resumes_df)} resumes")
        
        # Combine all job texts
        job_texts = []
        for _, row in jobs_df.iterrows():
            job_text = f"{row['description']} {row['requirements']} {row['skills']}"
            job_texts.append(job_text)
        
        # Combine all resume texts (limit to first 100 for performance)
        resume_texts = resumes_df['Resume'].head(100).tolist()
        
        # Extract skills using TF-IDF
        print("\n1. Extracting skills using TF-IDF...")
        all_texts = job_texts + resume_texts
        skill_candidates = self.extract_skills_with_tfidf(all_texts)
        
        # Extract adjectives and adverbs
        print("2. Extracting adjectives and adverbs...")
        adjectives = self.extract_adjectives_simple(all_texts)
        adverbs = self.extract_adverbs_simple(all_texts)
        
        # Get top skills
        print("3. Filtering and categorizing skills...")
        top_skills = [skill for skill, score in skill_candidates[:100]]
        
        # Categorize skills
        primary_skills, secondary_skills = self.categorize_skills(top_skills)
        
        # Get top adjectives and adverbs
        top_adjectives = [word for word, count in adjectives.most_common(30)]
        top_adverbs = [word for word, count in adverbs.most_common(20)]
        
        print(f"\nEXTRACTION RESULTS:")
        print(f"Primary Skills: {len(primary_skills)}")
        print(f"Secondary Skills: {len(secondary_skills)}")
        print(f"Adjectives: {len(top_adjectives)}")
        print(f"Adverbs: {len(top_adverbs)}")
        
        return {
            'primary_skills': primary_skills,
            'secondary_skills': secondary_skills,
            'adjectives': top_adjectives,
            'adverbs': top_adverbs,
            'skill_candidates': skill_candidates
        }

    def save_extracted_skills(self, extracted_skills, output_file='extracted_skills.py'):
        """Save extracted skills to a Python file"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Auto-extracted skills from real data\n")
            f.write("# Generated by SimpleSkillExtractor\n\n")
            
            f.write("PRIMARY_SKILLS = [\n")
            for skill in extracted_skills['primary_skills']:
                f.write(f"    '{skill}',\n")
            f.write("]\n\n")
            
            f.write("SECONDARY_SKILLS = [\n")
            for skill in extracted_skills['secondary_skills']:
                f.write(f"    '{skill}',\n")
            f.write("]\n\n")
            
            f.write("ADJECTIVES = [\n")
            for adj in extracted_skills['adjectives']:
                f.write(f"    '{adj}',\n")
            f.write("]\n\n")
            
            f.write("ADVERBS = [\n")
            for adv in extracted_skills['adverbs']:
                f.write(f"    '{adv}',\n")
            f.write("]\n")
        
        print(f"Skills saved to {output_file}")

def main():
    """Main function to extract skills"""
    extractor = SimpleSkillExtractor()
    
    # Extract skills from data
    extracted_skills = extractor.extract_all_skills_from_data(
        'data/itviec_jobs_undetected.csv',
        'data/UpdatedResumeDataSet.csv'
    )
    
    # Save to file
    extractor.save_extracted_skills(extracted_skills)
    
    # Print sample results
    print(f"\nSAMPLE EXTRACTED SKILLS:")
    print(f"\nTop 20 Primary Skills:")
    for skill in extracted_skills['primary_skills'][:20]:
        print(f"  - {skill}")
    
    print(f"\nTop 20 Secondary Skills:")
    for skill in extracted_skills['secondary_skills'][:20]:
        print(f"  - {skill}")
    
    print(f"\nTop 15 Adjectives:")
    for adj in extracted_skills['adjectives'][:15]:
        print(f"  - {adj}")
    
    print(f"\nTop 10 Adverbs:")
    for adv in extracted_skills['adverbs'][:10]:
        print(f"  - {adv}")
    
    print(f"\nNEXT STEPS:")
    print(f"1. Review extracted_skills.py")
    print(f"2. Update simple_data_pipeline.py to use these skills")
    print(f"3. Re-run the pipeline with real extracted skills")

if __name__ == "__main__":
    main()
