#!/usr/bin/env python3
"""
Quick Check - Xem nhanh kết quả phân loại suitability
"""

import pandas as pd
import sys

def quick_check(category=None, limit=5):
    """Xem nhanh kết quả theo category"""
    
    # Load data
    try:
        pairs_df = pd.read_csv('data/improved/job_candidate_pairs.csv')
        jobs_df = pd.read_csv('data/itviec_jobs_undetected.csv')
        print(f"Loaded {len(pairs_df)} job-candidate pairs")
    except FileNotFoundError:
        print("Error: Data files not found. Please run improved_data_pipeline.py first")
        return
    
    # Show distribution
    print(f"\n{'='*60}")
    print("SUITABILITY DISTRIBUTION")
    print(f"{'='*60}")
    
    label_counts = pairs_df['suitability_label'].value_counts().sort_index()
    total = len(pairs_df)
    labels = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}
    
    for label in [0, 1, 2]:
        count = label_counts.get(label, 0)
        percentage = (count / total) * 100
        print(f"{label} - {labels[label]:<20}: {count:3d} pairs ({percentage:5.1f}%)")
    
    # Show score ranges
    print(f"\n{'='*60}")
    print("SCORE RANGES")
    print(f"{'='*60}")
    
    for label in [0, 1, 2]:
        category_data = pairs_df[pairs_df['suitability_label'] == label]
        if len(category_data) > 0:
            min_score = category_data['overall_suitability'].min()
            max_score = category_data['overall_suitability'].max()
            mean_score = category_data['overall_suitability'].mean()
            print(f"{labels[label]:<20}: {min_score:.3f} - {max_score:.3f} (avg: {mean_score:.3f})")
    
    # Show specific category if requested
    if category is not None:
        show_category(pairs_df, jobs_df, category, limit)

def show_category(pairs_df, jobs_df, suitability_label, limit=5):
    """Hiển thị chi tiết một category"""
    labels = {0: 'NOT SUITABLE', 1: 'MODERATELY SUITABLE', 2: 'HIGHLY SUITABLE'}
    
    # Filter and sort data
    category_data = pairs_df[pairs_df['suitability_label'] == suitability_label]
    category_data = category_data.sort_values('overall_suitability', ascending=False)
    
    print(f"\n{'='*80}")
    print(f"{labels[suitability_label]} PAIRS (Top {min(limit, len(category_data))})")
    print(f"{'='*80}")
    
    if len(category_data) == 0:
        print("No pairs found in this category")
        return
    
    for idx, (_, row) in enumerate(category_data.head(limit).iterrows()):
        # Get job info
        job_num = int(row['job_id'].replace('JOB', ''))
        if job_num <= len(jobs_df):
            job_row = jobs_df.iloc[job_num - 1]
            job_title = job_row.get('title', 'N/A')
            job_company = job_row.get('company', 'N/A')
        else:
            job_title = 'N/A'
            job_company = 'N/A'
        
        print(f"\n{idx+1}. {row['job_id']} + {row['candidate_id']} (Score: {row['overall_suitability']:.3f})")
        print(f"   Job: {job_title}")
        print(f"   Company: {job_company}")
        print(f"   Skills Match - Primary: {row['primary_intersection']}/{row['job_primary_count']}, Secondary: {row['secondary_intersection']}/{row['job_secondary_count']}")
        print(f"   Similarity - Primary: {row['primary_skills_jaccard']:.3f}, Secondary: {row['secondary_skills_jaccard']:.3f}")

def main():
    """Main function"""
    if len(sys.argv) == 1:
        # No arguments - show overview
        print("QUICK SUITABILITY CHECK")
        print("="*60)
        quick_check()
        
        print(f"\n{'='*60}")
        print("USAGE:")
        print("python quick_check.py [category] [limit]")
        print("  category: 0=Not Suitable, 1=Moderately, 2=Highly")
        print("  limit: number of pairs to show (default: 5)")
        print("\nExamples:")
        print("  python quick_check.py 0     # Show Not Suitable pairs")
        print("  python quick_check.py 1 10  # Show top 10 Moderately Suitable")
        print("  python quick_check.py 2     # Show Highly Suitable pairs")
        
    else:
        # With arguments
        try:
            category = int(sys.argv[1])
            limit = int(sys.argv[2]) if len(sys.argv) > 2 else 5
            
            if category not in [0, 1, 2]:
                print("Error: Category must be 0, 1, or 2")
                return
            
            quick_check(category, limit)
            
        except ValueError:
            print("Error: Invalid arguments. Use integers only.")
            print("Usage: python quick_check.py [category] [limit]")

if __name__ == "__main__":
    main()
