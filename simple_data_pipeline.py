#!/usr/bin/env python3
"""
Simplified Data Pipeline for AI Job Matching Model
Dành riêng cho 2 file dữ liệu:
1. itviec_jobs_undetected.csv (Job Descriptions)
2. UpdatedResumeDataSet.csv (Candidate Resumes)

Tạo ra các file CSV theo cấu trúc bài báo nghiên cứu.
"""

import pandas as pd
import numpy as np
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.model_selection import train_test_split
import random
import os
from datetime import datetime

class SimpleJobMatchingPipeline:
    def __init__(self):
        self.jobs_df = None
        self.resumes_df = None
        
        # Predefined skill categories
        self.primary_skills = [
            'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
            'machine learning', 'deep learning', 'ai', 'data science', 'tensorflow', 
            'pytorch', 'scikit-learn', 'keras', 'opencv', 'nlp', 'computer vision',
            'react', 'angular', 'vue', 'node.js', 'express', 'django', 'flask',
            'spring', 'laravel', 'rails', 'asp.net', 'sql', 'mysql', 'postgresql'
        ]
        
        self.secondary_skills = [
            'git', 'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'linux', 'unix', 
            'ci/cd', 'jenkins', 'gitlab', 'github', 'jira', 'confluence', 'agile', 
            'scrum', 'communication', 'teamwork', 'leadership', 'problem solving',
            'mongodb', 'redis', 'elasticsearch', 'kafka', 'spark', 'hadoop'
        ]
        
        self.adjectives = [
            'innovative', 'creative', 'analytical', 'detail-oriented', 'collaborative',
            'proactive', 'dynamic', 'flexible', 'reliable', 'efficient', 'effective',
            'strategic', 'technical', 'experienced', 'skilled', 'professional',
            'motivated', 'dedicated', 'passionate', 'results-driven'
        ]
        
        self.adverbs = [
            'efficiently', 'effectively', 'successfully', 'independently', 'collaboratively',
            'systematically', 'strategically', 'consistently', 'professionally', 'rapidly',
            'accurately', 'thoroughly', 'creatively', 'analytically', 'proactively'
        ]

    def load_data(self):
        """Load job and resume data"""
        try:
            self.jobs_df = pd.read_csv('data/itviec_jobs_undetected.csv')
            self.resumes_df = pd.read_csv('data/UpdatedResumeDataSet.csv')
            
            print(f"✓ Loaded {len(self.jobs_df)} job descriptions")
            print(f"✓ Loaded {len(self.resumes_df)} candidate resumes")
            
            return True
        except FileNotFoundError as e:
            print(f"❌ Error loading data: {e}")
            return False

    def clean_text(self, text):
        """Clean and preprocess text"""
        if pd.isna(text):
            return ""
        
        text = str(text).lower()
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        text = ' '.join(text.split())
        return text

    def extract_skills_from_text(self, text, skill_list):
        """Extract skills from text"""
        if pd.isna(text):
            return []
        
        text_clean = self.clean_text(text)
        found_skills = []
        
        for skill in skill_list:
            if skill.lower() in text_clean:
                found_skills.append(skill)
        
        return found_skills

    def extract_words_from_text(self, text, word_list):
        """Extract adjectives/adverbs from text"""
        if pd.isna(text):
            return []
        
        text_clean = self.clean_text(text)
        found_words = []
        
        for word in word_list:
            if word.lower() in text_clean:
                count = text_clean.count(word.lower())
                found_words.extend([word] * count)
        
        return found_words

    def create_job_skills_data(self):
        """Extract skills from job descriptions"""
        primary_skills_data = []
        secondary_skills_data = []
        adjectives_data = []
        adverbs_data = []
        
        for idx, row in self.jobs_df.iterrows():
            job_id = f"JOB{str(row['id']).zfill(3)}"
            
            # Combine all job text
            job_text = f"{row['description']} {row['requirements']} {row['skills']}"
            
            # Extract primary skills
            primary_skills = self.extract_skills_from_text(job_text, self.primary_skills)
            for skill in set(primary_skills):
                primary_skills_data.append({
                    'id': len(primary_skills_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'skill_name': skill,
                    'skill_level': random.choice(['Intermediate', 'Advanced', 'Expert']),
                    'years_experience': 'NA',
                    'importance_weight': round(random.uniform(0.6, 1.0), 2),
                    'skill_category': self.categorize_skill(skill)
                })
            
            # Extract secondary skills
            secondary_skills = self.extract_skills_from_text(job_text, self.secondary_skills)
            for skill in set(secondary_skills):
                secondary_skills_data.append({
                    'id': len(secondary_skills_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'skill_name': skill,
                    'proficiency_level': random.choice(['Required', 'Preferred', 'Nice to have']),
                    'importance_weight': round(random.uniform(0.3, 0.8), 2),
                    'skill_category': self.categorize_skill(skill)
                })
            
            # Extract adjectives
            adjectives = self.extract_words_from_text(job_text, self.adjectives)
            for adj in set(adjectives):
                adjectives_data.append({
                    'id': len(adjectives_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'adjective': adj,
                    'context': random.choice(['work environment', 'requirements', 'team culture']),
                    'frequency': adjectives.count(adj),
                    'sentiment_score': round(random.uniform(0.5, 1.0), 2)
                })
            
            # Extract adverbs
            adverbs = self.extract_words_from_text(job_text, self.adverbs)
            for adv in set(adverbs):
                adverbs_data.append({
                    'id': len(adverbs_data) + 1,
                    'type': 'job',
                    'entity_id': job_id,
                    'adverb': adv,
                    'context': random.choice(['task execution', 'work style', 'performance']),
                    'frequency': adverbs.count(adv),
                    'intensity_score': round(random.uniform(0.5, 1.0), 2)
                })
        
        return (pd.DataFrame(primary_skills_data), pd.DataFrame(secondary_skills_data),
                pd.DataFrame(adjectives_data), pd.DataFrame(adverbs_data))

    def create_candidate_skills_data(self):
        """Extract skills from candidate resumes"""
        primary_skills_data = []
        secondary_skills_data = []
        adjectives_data = []
        adverbs_data = []
        candidates_data = []
        
        for idx, row in self.resumes_df.iterrows():
            if idx >= 100:  # Limit to first 100 candidates for demo
                break
                
            candidate_id = f"CAND{str(idx+1).zfill(3)}"
            resume_text = str(row['Resume'])
            category = str(row['Category'])
            
            # Create candidate record
            candidates_data.append({
                'candidate_id': candidate_id,
                'name': f"Candidate {idx+1}",
                'email': f"candidate{idx+1}@email.com",
                'phone': f"090{random.randint(1000000, 9999999)}",
                'experience_years': random.randint(1, 15),
                'education_level': random.choice(['Bachelor', 'Master', 'PhD']),
                'current_position': category,
                'summary': resume_text[:200] + "...",
                'cv_text': resume_text
            })
            
            # Extract primary skills
            primary_skills = self.extract_skills_from_text(resume_text, self.primary_skills)
            for skill in set(primary_skills):
                primary_skills_data.append({
                    'id': len(primary_skills_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'skill_name': skill,
                    'skill_level': random.choice(['Beginner', 'Intermediate', 'Advanced', 'Expert']),
                    'years_experience': random.randint(1, 10),
                    'importance_weight': 'NA',
                    'skill_category': self.categorize_skill(skill)
                })
            
            # Extract secondary skills
            secondary_skills = self.extract_skills_from_text(resume_text, self.secondary_skills)
            for skill in set(secondary_skills):
                secondary_skills_data.append({
                    'id': len(secondary_skills_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'skill_name': skill,
                    'proficiency_level': random.choice(['Basic', 'Good', 'Advanced', 'Expert']),
                    'importance_weight': 'NA',
                    'skill_category': self.categorize_skill(skill)
                })
            
            # Extract adjectives
            adjectives = self.extract_words_from_text(resume_text, self.adjectives)
            for adj in set(adjectives):
                adjectives_data.append({
                    'id': len(adjectives_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'adjective': adj,
                    'context': random.choice(['self-description', 'experience', 'achievements']),
                    'frequency': adjectives.count(adj),
                    'sentiment_score': round(random.uniform(0.5, 1.0), 2)
                })
            
            # Extract adverbs
            adverbs = self.extract_words_from_text(resume_text, self.adverbs)
            for adv in set(adverbs):
                adverbs_data.append({
                    'id': len(adverbs_data) + 1,
                    'type': 'candidate',
                    'entity_id': candidate_id,
                    'adverb': adv,
                    'context': random.choice(['achievements', 'work style', 'performance']),
                    'frequency': adverbs.count(adv),
                    'intensity_score': round(random.uniform(0.5, 1.0), 2)
                })
        
        return (pd.DataFrame(primary_skills_data), pd.DataFrame(secondary_skills_data),
                pd.DataFrame(adjectives_data), pd.DataFrame(adverbs_data),
                pd.DataFrame(candidates_data))

    def categorize_skill(self, skill):
        """Categorize skills"""
        programming_langs = ['python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust']
        ai_ml = ['machine learning', 'deep learning', 'ai', 'data science', 'tensorflow', 'pytorch']
        frameworks = ['react', 'angular', 'vue', 'django', 'flask', 'spring', 'laravel']
        databases = ['sql', 'mysql', 'postgresql', 'mongodb', 'redis']
        devops = ['docker', 'kubernetes', 'aws', 'azure', 'gcp', 'ci/cd', 'jenkins']
        
        skill_lower = skill.lower()
        
        if skill_lower in programming_langs:
            return 'Programming Language'
        elif skill_lower in ai_ml:
            return 'AI/ML'
        elif skill_lower in frameworks:
            return 'Framework'
        elif skill_lower in databases:
            return 'Database'
        elif skill_lower in devops:
            return 'DevOps'
        else:
            return 'Other'

    def jaccard_similarity(self, set1, set2):
        """Calculate Jaccard similarity"""
        if len(set1) == 0 and len(set2) == 0:
            return 1.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0

    def create_job_candidate_pairs(self, job_skills_dfs, candidate_skills_dfs):
        """Create job-candidate pairs with similarity scores"""
        job_primary, job_secondary, job_adj, job_adv = job_skills_dfs
        cand_primary, cand_secondary, cand_adj, cand_adv, candidates = candidate_skills_dfs
        
        pairs_data = []
        features_data = []
        
        # Get unique job and candidate IDs
        job_ids = job_primary['entity_id'].unique()
        candidate_ids = candidates['candidate_id'].unique()
        
        pair_id = 1
        
        for job_id in job_ids:
            # Select random candidates for this job (5 per job)
            selected_candidates = random.sample(list(candidate_ids), min(5, len(candidate_ids)))
            
            for candidate_id in selected_candidates:
                # Get skills for job and candidate
                job_primary_skills = set(job_primary[job_primary['entity_id'] == job_id]['skill_name'])
                job_secondary_skills = set(job_secondary[job_secondary['entity_id'] == job_id]['skill_name'])
                job_adjectives = set(job_adj[job_adj['entity_id'] == job_id]['adjective'])
                job_adverbs = set(job_adv[job_adv['entity_id'] == job_id]['adverb'])
                
                cand_primary_skills = set(cand_primary[cand_primary['entity_id'] == candidate_id]['skill_name'])
                cand_secondary_skills = set(cand_secondary[cand_secondary['entity_id'] == candidate_id]['skill_name'])
                cand_adjectives = set(cand_adj[cand_adj['entity_id'] == candidate_id]['adjective'])
                cand_adverbs = set(cand_adv[cand_adv['entity_id'] == candidate_id]['adverb'])
                
                # Calculate Jaccard similarities
                primary_jaccard = self.jaccard_similarity(job_primary_skills, cand_primary_skills)
                secondary_jaccard = self.jaccard_similarity(job_secondary_skills, cand_secondary_skills)
                adjectives_jaccard = self.jaccard_similarity(job_adjectives, cand_adjectives)
                adverbs_jaccard = self.jaccard_similarity(job_adverbs, cand_adverbs)
                
                # Calculate overall suitability (weighted average)
                overall_suitability = (0.4 * primary_jaccard + 0.3 * secondary_jaccard + 
                                     0.15 * adjectives_jaccard + 0.15 * adverbs_jaccard)
                
                # Create suitability label (adjusted thresholds)
                if overall_suitability >= 0.25:
                    suitability_label = 2  # Highly Suitable
                elif overall_suitability >= 0.1:
                    suitability_label = 1  # Moderately Suitable
                else:
                    suitability_label = 0  # Not Suitable
                
                # Create pair record
                pairs_data.append({
                    'pair_id': pair_id,
                    'job_id': job_id,
                    'candidate_id': candidate_id,
                    'primary_skills_jaccard': round(primary_jaccard, 3),
                    'secondary_skills_jaccard': round(secondary_jaccard, 3),
                    'adjectives_jaccard': round(adjectives_jaccard, 3),
                    'adverbs_jaccard': round(adverbs_jaccard, 3),
                    'overall_suitability': round(overall_suitability, 3),
                    'suitability_label': suitability_label
                })
                
                pair_id += 1
        
        return pd.DataFrame(pairs_data)

    def run_complete_pipeline(self):
        """Run the complete pipeline"""
        print("🚀 STARTING SIMPLE AI JOB MATCHING PIPELINE")
        print("=" * 60)
        
        # Create output directory
        os.makedirs('data', exist_ok=True)
        
        # Step 1: Load data
        print("\n1. Loading data...")
        if not self.load_data():
            return False
        
        # Step 2: Extract job skills
        print("\n2. Extracting skills from job descriptions...")
        job_skills_dfs = self.create_job_skills_data()
        
        # Step 3: Extract candidate skills
        print("\n3. Extracting skills from candidate resumes...")
        candidate_skills_dfs = self.create_candidate_skills_data()
        
        # Step 4: Create job-candidate pairs
        print("\n4. Creating job-candidate pairs...")
        pairs_df = self.create_job_candidate_pairs(job_skills_dfs, candidate_skills_dfs)
        
        # Step 5: Save all data
        print("\n5. Saving data to CSV files...")
        
        # Save job skills
        job_primary, job_secondary, job_adj, job_adv = job_skills_dfs
        job_primary.to_csv('data/job_primary_skills.csv', index=False)
        job_secondary.to_csv('data/job_secondary_skills.csv', index=False)
        job_adj.to_csv('data/job_adjectives.csv', index=False)
        job_adv.to_csv('data/job_adverbs.csv', index=False)
        
        # Save candidate skills
        cand_primary, cand_secondary, cand_adj, cand_adv, candidates = candidate_skills_dfs
        cand_primary.to_csv('data/candidate_primary_skills.csv', index=False)
        cand_secondary.to_csv('data/candidate_secondary_skills.csv', index=False)
        cand_adj.to_csv('data/candidate_adjectives.csv', index=False)
        cand_adv.to_csv('data/candidate_adverbs.csv', index=False)
        candidates.to_csv('data/candidates.csv', index=False)
        
        # Save pairs
        pairs_df.to_csv('data/job_candidate_pairs.csv', index=False)
        
        # Combine all skills data
        all_primary = pd.concat([job_primary, cand_primary], ignore_index=True)
        all_secondary = pd.concat([job_secondary, cand_secondary], ignore_index=True)
        all_adjectives = pd.concat([job_adj, cand_adj], ignore_index=True)
        all_adverbs = pd.concat([job_adv, cand_adv], ignore_index=True)
        
        all_primary.to_csv('data/primary_skills.csv', index=False)
        all_secondary.to_csv('data/secondary_skills.csv', index=False)
        all_adjectives.to_csv('data/adjectives.csv', index=False)
        all_adverbs.to_csv('data/adverbs.csv', index=False)
        
        # Print summary
        print("\n" + "=" * 60)
        print("✅ PIPELINE COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        print(f"\nGenerated Files:")
        print(f"📁 Job Skills:")
        print(f"   - job_primary_skills.csv: {len(job_primary)} records")
        print(f"   - job_secondary_skills.csv: {len(job_secondary)} records")
        print(f"   - job_adjectives.csv: {len(job_adj)} records")
        print(f"   - job_adverbs.csv: {len(job_adv)} records")
        
        print(f"\n📁 Candidate Skills:")
        print(f"   - candidate_primary_skills.csv: {len(cand_primary)} records")
        print(f"   - candidate_secondary_skills.csv: {len(cand_secondary)} records")
        print(f"   - candidate_adjectives.csv: {len(cand_adj)} records")
        print(f"   - candidate_adverbs.csv: {len(cand_adv)} records")
        print(f"   - candidates.csv: {len(candidates)} records")
        
        print(f"\n📁 Combined Data:")
        print(f"   - primary_skills.csv: {len(all_primary)} records")
        print(f"   - secondary_skills.csv: {len(all_secondary)} records")
        print(f"   - adjectives.csv: {len(all_adjectives)} records")
        print(f"   - adverbs.csv: {len(all_adverbs)} records")
        print(f"   - job_candidate_pairs.csv: {len(pairs_df)} records")
        
        # Label distribution
        label_dist = pairs_df['suitability_label'].value_counts().sort_index()
        print(f"\n📊 Suitability Label Distribution:")
        print(f"   Not Suitable (0): {label_dist.get(0, 0)}")
        print(f"   Moderately Suitable (1): {label_dist.get(1, 0)}")
        print(f"   Highly Suitable (2): {label_dist.get(2, 0)}")
        
        print(f"\n🎯 Next Steps:")
        print(f"   1. Use job_candidate_pairs.csv for machine learning")
        print(f"   2. Train models: Linear Regression, Decision Tree, AdaBoost, XGBoost")
        print(f"   3. Target accuracy: 95.14% (XGBoost)")
        
        return True

if __name__ == "__main__":
    pipeline = SimpleJobMatchingPipeline()
    pipeline.run_complete_pipeline()
